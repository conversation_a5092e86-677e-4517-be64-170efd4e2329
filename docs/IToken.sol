// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

interface IZeusToken {

    function registerPrediction() external;

    function mintTokens(address _account, uint256 _outcomeIndex, uint256 _amount) external;
    
    function burnTokens(address _account, uint256 _outcomeIndex, uint256 _amount) external;
    
    function getTokenId(address _prediction, uint256 _outcomeIndex) external view returns (uint256);
    
    function getTotalSupply(address _prediction, uint256 _outcomeIndex) external view returns (uint256);
    
    function s_factoryFeePercent() external view returns (uint256);
    
    function s_creatorFeePercent() external view returns (uint256);
    
    function balanceOf(address account, uint256 id) external view returns (uint256);
}