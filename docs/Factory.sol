// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { Clones } from "@openzeppelin/contracts/proxy/Clones.sol";
import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { IERC20 } from  "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import { ECDSA } from "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import { ReentrancyGuard } from  "@openzeppelin/contracts/utils/ReentrancyGuard.sol";
import { MessageHashUtils } from "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";
import { IRebalancer } from  "./interfaces/IRebalancer.sol";
import { IPrediction } from  "./interfaces/IPrediction.sol";
import { IZeusToken } from  "./interfaces/IToken.sol";

/**
 * @title Prediction Factory
 * @dev Manages the lifecycle of prediction bets
 */
contract Factory is Ownable, ReentrancyGuard {
    using ECDSA for bytes32;

    IERC20 s_collateralToken;
    IRebalancer s_rebalancer;
    IPrediction s_template;
    IZeusToken s_token;

    address[] s_predictionAddresses;
    uint256 s_minimalInitialLiquidity;

    mapping ( address prediction => IPrediction.Metadata ) m_predictions;
    mapping ( bytes32 polygonHash => address[] predictionsByPolygon ) m_polygons;
    mapping ( bytes32 signatureHash => bool isUsed ) m_usedSignatures;
    mapping ( address resolver => bool isSafe ) m_safeResolvers;

    // Events
    event Factory_PredictionCreated(
        address indexed prediction,
        bytes32 indexed polygonHash,
        string[] conditions,
        uint256 startDate,
        uint256 endDate,
        uint256 outcomeSlot,
        address[] resolvers,
        address creator
    );
    event Factory_ResolverAdded(address indexed resolver);
    event Factory_ResolverRekoved(address indexed resolver);
    event Factory_MinimalInitialLiquiditySet(uint256 amount);
    
    /// @notice Gas Savings: since Solidity 0.8.4, custom errors in most gas-efficient
    error Factory_NeedResolver();
    error Factory_InvalidAddress();
    error Factory_UnsafeResolver();
    error Factory_InvalidSignature();
    error Factory_InsufficientAmount();
    error Factory_SignatureAlreadyUsed();
    error Factory_InvalidPredictionDates();
    error Factory_PredictionAlreadyRegistered();
    error Factory_InsufficientInitialLiquidity();
    error Factory_ConditionsMustMatchOutcomeSlot();
    error Factory_InsufficientAllowance(uint256 allowance, uint256 required);

    modifier onlyValidAddress(address _addr) {
        if (_addr == address(0)) revert Factory_InvalidAddress();
        _;
    }

    constructor(
        address _collateralToken,
        address _rebalancer,
        address _template,
        address _token
    ) Ownable(msg.sender) {
        if (
            _collateralToken == address(0) ||
            _rebalancer == address(0) ||
            _template == address(0) ||
            _token == address(0)
        ) revert Factory_InvalidAddress();
        
        s_token = IZeusToken(_token);
        s_collateralToken = IERC20(_collateralToken);
        s_rebalancer = IRebalancer(_rebalancer);
        s_template = IPrediction(_template);
        s_minimalInitialLiquidity = 1 * 1e6; // 1 USDC (6 decimals)
    }

    function createPrediction(
        IPrediction.Metadata calldata _input,
        bytes calldata _signature,
        uint256 _initialLiquidity
    ) external nonReentrant returns(address prediction_, bytes32 polygonHash_) {
        // REVIEW: add more complex signature on backend. in future is hashed the polygon on backend
        bytes32 hash = keccak256(abi.encodePacked(_input.polygonId));
        bytes32 ethHash = MessageHashUtils.toEthSignedMessageHash(hash);
        
        if (m_usedSignatures[ethHash]) revert Factory_SignatureAlreadyUsed();
        
        address signer = ECDSA.recover(ethHash, _signature);
        if (signer != owner()) revert Factory_InvalidSignature();
        
        m_usedSignatures[ethHash] = true;

        // REVIEW: need a revert if resolved == true, because cannot permit a prediction already resolved.
        if (_input.resolvers.length == 0) revert Factory_NeedResolver();
        for (uint256 i = 0; i < _input.resolvers.length; i++) {
            if (!m_safeResolvers[_input.resolvers[i]]) revert Factory_UnsafeResolver();
        }

        // REVIEW: what is the max _input.outcomeSlot? can includes infinity outcomes? uses uint8 is a good idea?
        if (
            _input.outcomeSlot < 2 ||
            _input.outcomeSlot != _input.conditions.length
        ) revert Factory_ConditionsMustMatchOutcomeSlot();
        
        if (
            _input.startDate <= block.timestamp ||
            _input.startDate > _input.endDate
        ) revert Factory_InvalidPredictionDates();

        (prediction_, polygonHash_) = _setPrediction(_input);

        if (_initialLiquidity < s_minimalInitialLiquidity * _input.outcomeSlot) {
            revert Factory_InsufficientInitialLiquidity();
        }
        uint256 allowance = s_collateralToken.allowance(msg.sender, address(this));
        if (allowance < _initialLiquidity) {
            revert Factory_InsufficientAllowance(allowance, _initialLiquidity);
        }
        
        require(
            s_collateralToken.transferFrom(msg.sender, address(this), _initialLiquidity),
            "Initial liquidity transfer failed"
        );
        require(
            s_collateralToken.approve(prediction_, _initialLiquidity),
            "Factory approve to prediction failed"
        );
    
        uint256 liquidityPerOutcome = _initialLiquidity / _input.outcomeSlot;
        for (uint256 i = 0; i < _input.outcomeSlot; i++) {
            IPrediction(prediction_).buyTokens(i, liquidityPerOutcome);
        }

        return (prediction_, polygonHash_);
    }

    function collectFactoryFees(address _prediction) external onlyOwner {
        IPrediction(_prediction).collectFactoryFees();
    }

    function getPredictionStruct(
        address _prediction
    ) external onlyValidAddress(_prediction) view
        returns (IPrediction.Metadata memory prediction_)
    {
        return m_predictions[_prediction];
    }

    function getPrediction(
        address _prediction
    ) external onlyValidAddress(_prediction) view
        returns (
            address creator,
            address[] memory resolvers,
            bytes32 polygonHash,
            string[] memory conditions,
            uint256 startDate,
            uint256 endDate,
            uint256 outcomeSlot,
            bool resolved
        )
    {
        IPrediction.Metadata storage meta = m_predictions[_prediction];
        return (
            IPrediction(_prediction).getCreator(),
            meta.resolvers,
            getPolygonHash(_prediction),
            meta.conditions,
            meta.startDate,
            meta.endDate,
            meta.outcomeSlot,
            meta.resolved
        );
    }

    function getPolygonHash(
        address _prediction
    ) public view onlyValidAddress(_prediction) returns (bytes32) {
        IPrediction.Metadata storage meta = m_predictions[_prediction];
        return keccak256(abi.encodePacked(meta.polygonId, meta.polygonCid));
    }

    function getPredictionsByPolygon(
        bytes32 _polygonHash
    ) external view returns (IPrediction.Metadata[] memory) {
        address[] memory preds = m_polygons[_polygonHash];
        uint256 len = preds.length;
        IPrediction.Metadata[] memory paged = new IPrediction.Metadata[](len);

        for (uint256 i = 0; i < len; i++) {
            paged[i] = m_predictions[preds[i]];
        }

        return paged;
    }

    function getTotalPredictionsByPolygon(
        bytes32 _polygonHash
    ) external view returns (uint256) {
        return m_polygons[_polygonHash].length;
    }

    function getPredictions(
        uint256 _offset, uint256 _limit
    ) external view returns (IPrediction.Metadata[] memory) {
        // REVIEW: improve this for better on-chain performance
        uint256 total = s_predictionAddresses.length;

        if (_offset >= total) {
            return new IPrediction.Metadata[](0);
        }

        uint256 end = _offset + _limit;
        if (end > total) {
            end = total;
        }

        uint256 resultLength = end - _offset;
        IPrediction.Metadata[] memory paged = new IPrediction.Metadata[](resultLength);

        for (uint256 i = 0; i < resultLength; i++) {
            address predictionAddr = s_predictionAddresses[_offset + i];
            paged[i] = m_predictions[predictionAddr];
        }

        return paged;
    }

    function getTotalPredictions() external view returns (uint256) {
        return s_predictionAddresses.length;
    }

    // REVIEW: maybe create a mapping for faster access to pending resolutions predictions? m_resolvablePredictions and m_pendingResolutions 
    function getResolvablePredictions(
        uint256 _offset, uint256 _limit
    ) external view returns (IPrediction.Metadata[] memory) {
        // REVIEW: improve this for better on-chain performance
        uint256 total = s_predictionAddresses.length;

        if (_offset >= total) {
            return new IPrediction.Metadata[](0);
        }

        uint256 end = _offset + _limit;
        if (end > total) {
            end = total;
        }

        IPrediction.Metadata[] memory temp = new IPrediction.Metadata[](_limit);
        uint256 count;

        for (uint256 i = _offset; i < end; i++) {
            IPrediction.Metadata storage meta = m_predictions[s_predictionAddresses[i]];
            if (!meta.resolved && block.timestamp >= meta.endDate) {
                temp[count++] = meta;
            }
        }

        IPrediction.Metadata[] memory paged = new IPrediction.Metadata[](count);
        for (uint256 j = 0; j < count; j++) {
            paged[j] = temp[j];
        }

        return paged;
    }

    // REVIEW: maybe create a mapping for faster access to pending resolutions predictions? m_resolvablePredictions and m_pendingResolutions
    function getPendingResolutions() external view returns (IPrediction.Metadata[] memory) {
        uint256 total = s_predictionAddresses.length;
        uint256 count;

        for (uint256 i = 0; i < total; i++) {
            IPrediction.Metadata storage meta = m_predictions[s_predictionAddresses[i]];
            if (!meta.resolved && block.timestamp < meta.endDate) {
                count++;
            }
        }

        IPrediction.Metadata[] memory pending = new IPrediction.Metadata[](count);
        uint256 index;

        for (uint256 i = 0; i < total; i++) {
            IPrediction.Metadata storage meta = m_predictions[s_predictionAddresses[i]];
            if (!meta.resolved && block.timestamp < meta.endDate) {
                pending[index++] = meta;
            }
        }

        return pending;
    }

    function addSafeResolver(
        address _resolver
    ) external onlyOwner onlyValidAddress(_resolver) {
        // REVIEW: need a validation if the resolver already exist?
        m_safeResolvers[_resolver] = true;
        emit Factory_ResolverAdded(_resolver);
    }

    function revokeResolver(
        address _resolver
    ) external onlyOwner onlyValidAddress(_resolver) {
        // REVIEW: need a validation if the resolver already setted if false?
        m_safeResolvers[_resolver] = false;
        emit Factory_ResolverRekoved(_resolver);
    }

    function setMinimalInitialLiquidity(uint256 _amount) external onlyOwner {
        if (_amount == 0) revert Factory_InsufficientAmount();
        s_minimalInitialLiquidity = _amount * 1e6;
        emit Factory_MinimalInitialLiquiditySet(s_minimalInitialLiquidity);
    }

    function _setPrediction(
        IPrediction.Metadata calldata _input
    ) internal returns(address prediction_, bytes32 polygonHash_) {
        prediction_ = Clones.clone(address(s_template));
        if (prediction_ == address(0)) revert Factory_InvalidAddress();

        s_predictionAddresses.push(prediction_);
        IPrediction(prediction_).createPool(
            _input,
            msg.sender,
            address(this),
            address(s_collateralToken),
            address(s_rebalancer),
            address(s_token)
        );

        polygonHash_ = getPolygonHash(prediction_);
        m_polygons[polygonHash_].push(prediction_);
        m_predictions[prediction_] = _input;

        emit Factory_PredictionCreated(
            prediction_,
            polygonHash_,
            _input.conditions,
            _input.startDate,
            _input.endDate,
            _input.outcomeSlot,
            _input.resolvers,
            msg.sender
        );

        return (prediction_, polygonHash_);
    }
}