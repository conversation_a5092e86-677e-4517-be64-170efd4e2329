// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { ERC1155 } from "@openzeppelin/contracts/token/ERC1155/ERC1155.sol";
import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { IPrediction } from "./interfaces/IPrediction.sol";

/**
 * @title Outcome Token
 * @dev ERC1155 token for representing outcomes shares in prediction markets
 */
contract ZeusToken is ERC1155, Ownable {
    // REVIEW: verify the possible of set outcomes tokens with 6 decimals for better resolution comparating with USCD.
    uint256 public constant MAX_FEE = 1000; // 10% in basis points
    uint256 public s_factoryFeePercent;
    uint256 public s_creatorFeePercent;

    // REVIEW: this mapping is redundant? The Factory contract already has a mapping of predictions.
    mapping ( address prediction => bool registered ) public m_predictions;
    mapping ( uint256 tokenId => uint256 totalSupply ) public m_totalSupply;

    // Events
    event Token_PredictionRegistered(address indexed prediction);

    /// @notice Gas Savings: since Solidity 0.8.4, custom errors in most gas-efficient
    error Token_InvalidOutcome();
    error Token_InvalidAddress();
    error Token_InvalidPercent();
    error Token_AlreadyRegistered();
    error Token_NotRegistered();

    constructor() ERC1155("") Ownable(msg.sender) {
        // Basis Points be more accurate
        s_factoryFeePercent = 100; // 1% factory fee
        s_creatorFeePercent = 50; // 0.5% creator fee
    }

    // REVIEW: this function cannot called anywhere, need a more robust Access Control. Is calling only in the Prediction.sol. but is a dinamic generated address by Clone on Factory.sol
    // REVIEW: not defined Access Control for tests, but include Access Control for prodution.
    function registerPrediction() external {
        if (msg.sender == address(0)) revert Token_InvalidAddress();
        if (m_predictions[msg.sender]) revert Token_AlreadyRegistered();
        m_predictions[msg.sender] = true;
        emit Token_PredictionRegistered(msg.sender);
    }

    // REVIEW: Alert! need m_predictions[msg.sender] to call mintTokens() in the Token contract.
    function mintTokens(
        address _account,
        uint256 _outcomeIndex,
        uint256 _amount
    ) external {
        if (_account == address(0)) revert Token_InvalidAddress();
        if (!m_predictions[msg.sender]) revert Token_NotRegistered();
        uint256 tokenId = getTokenId(msg.sender, _outcomeIndex);
        m_totalSupply[tokenId] += _amount;
        _mint(_account, tokenId, _amount, "");
    }

    // REVIEW: Alert! need m_predictions[msg.sender] to call burnTokens() in the Token contract.
    function burnTokens(
        address _account,
        uint256 _outcomeIndex,
        uint256 _amount
    ) external {
        if (_account == address(0)) revert Token_InvalidAddress();
        if (!m_predictions[msg.sender]) revert Token_NotRegistered();
        uint256 tokenId = getTokenId(msg.sender, _outcomeIndex);
        m_totalSupply[tokenId] -= _amount;
        _burn(_account, tokenId, _amount);
    }

    // REVIEW: Alert! need owner to call setFactoryFee() in the Token contract.
    function setFactoryFee(uint256 _factoryFeePercent) external onlyOwner {
        // REVIEW: What is the MAX_FEE? 10%?
        if (_factoryFeePercent > MAX_FEE) revert Token_InvalidPercent();
        s_factoryFeePercent = _factoryFeePercent;
    }

    // REVIEW: Alert! need owner to call setCreatorFee() in the Token contract.
    function setCreatorFee(uint256 _creatorFeePercent) external onlyOwner {
        // REVIEW: What is the MAX_FEE? 10%?
        if (_creatorFeePercent > MAX_FEE) revert Token_InvalidPercent();
        s_creatorFeePercent = _creatorFeePercent;
    }

    function getTokenId(
        address _prediction, uint256 _outcomeIndex
    ) public view returns (uint256) {
        _validateMeta(_prediction, _outcomeIndex);
        return uint256(keccak256(abi.encodePacked(_prediction, _outcomeIndex)));
    }

    function getPredictionExists(
        address _prediction
    ) external view returns (bool) {
        if (_prediction == address(0)) revert Token_InvalidAddress();
        return m_predictions[_prediction];
    }

    function getTotalSupply(
        address _prediction, uint256 _outcomeIndex
    ) external view returns (uint256) {
        _validateMeta(_prediction, _outcomeIndex);
        return m_totalSupply[getTokenId(_prediction, _outcomeIndex)];
    }

    function _validateMeta(
        address _prediction, uint256 _outcomeIndex
    ) internal view {
        if (_prediction == address(0)) revert Token_InvalidAddress();
        IPrediction.Metadata memory meta = IPrediction(_prediction).getMetadata();
        if (_outcomeIndex > meta.outcomeSlot) revert Token_InvalidOutcome();
    }
}