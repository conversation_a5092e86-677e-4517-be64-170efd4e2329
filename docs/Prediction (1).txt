{"abi": [{"type": "function", "name": "buyTokens", "inputs": [{"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}, {"name": "_collateralAmount", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokenAmount_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "claimRewards", "inputs": [{"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "payout_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "collectCreatorFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "collectFactoryFees", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "createPool", "inputs": [{"name": "_input", "type": "tuple", "internalType": "struct IPrediction.Metadata", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}, {"name": "_creator", "type": "address", "internalType": "address"}, {"name": "_factory", "type": "address", "internalType": "address"}, {"name": "_collateralToken", "type": "address", "internalType": "address"}, {"name": "_rebalancer", "type": "address", "internalType": "address"}, {"name": "_token", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "getCreator", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "getLiquidity", "inputs": [{"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getMetadata", "inputs": [], "outputs": [{"name": "", "type": "tuple", "internalType": "struct IPrediction.Metadata", "components": [{"name": "polygonId", "type": "string", "internalType": "string"}, {"name": "polygonCid", "type": "string", "internalType": "string"}, {"name": "conditions", "type": "string[]", "internalType": "string[]"}, {"name": "outcomeSlot", "type": "uint256", "internalType": "uint256"}, {"name": "startDate", "type": "uint256", "internalType": "uint256"}, {"name": "endDate", "type": "uint256", "internalType": "uint256"}, {"name": "resolvers", "type": "address[]", "internalType": "address[]"}, {"name": "resolved", "type": "bool", "internalType": "bool"}]}], "stateMutability": "view"}, {"type": "function", "name": "getTotalFeesByCreator", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalFeesByFactory", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getTotalLiquidity", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "onERC1155BatchReceived", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC1155Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "setResolved", "inputs": [{"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "success_", "type": "bool", "internalType": "bool"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "Prediction_FeesCollected", "inputs": [{"name": "sender", "type": "address", "indexed": true, "internalType": "address"}, {"name": "prediction", "type": "address", "indexed": false, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Prediction_LiquidityUpdated", "inputs": [{"name": "outcome", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "outcomeLiquidityAdd", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalOutcomeLiquidity", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "totalPredictionLiquidity", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Prediction_PoolCreated", "inputs": [{"name": "prediction", "type": "address", "indexed": true, "internalType": "address"}, {"name": "s_creator", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Prediction_Resolved", "inputs": [{"name": "outcomeIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Prediction_RewardClaimed", "inputs": [{"name": "user", "type": "address", "indexed": true, "internalType": "address"}, {"name": "amount", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Prediction_TokensBought", "inputs": [{"name": "prediction", "type": "address", "indexed": true, "internalType": "address"}, {"name": "outcomeIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "tokenAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralPaid", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "Prediction_AlreadyResolved", "inputs": []}, {"type": "error", "name": "Prediction_AmountCannotBeZero", "inputs": []}, {"type": "error", "name": "Prediction_FeesAlreadyCollected", "inputs": []}, {"type": "error", "name": "Prediction_InsufficientLiquidity", "inputs": []}, {"type": "error", "name": "Prediction_InvalidAddress", "inputs": []}, {"type": "error", "name": "Prediction_InvalidOutcome", "inputs": []}, {"type": "error", "name": "Prediction_NoTokensToClaim", "inputs": []}, {"type": "error", "name": "Prediction_NotResolved", "inputs": []}, {"type": "error", "name": "Prediction_PoolAlreadyExists", "inputs": []}, {"type": "error", "name": "Prediction_PoolNotActive", "inputs": []}, {"type": "error", "name": "Prediction_PredictionNotEnded", "inputs": []}, {"type": "error", "name": "Prediction_PredictionNotResolved", "inputs": []}, {"type": "error", "name": "Prediction_UnsafeResolver", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}]}