{"abi": [{"type": "constructor", "inputs": [{"name": "_factory", "type": "address", "internalType": "address"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "owner", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "renounceOwnership", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "resolver<PERSON><PERSON>er", "inputs": [{"name": "_prediction", "type": "address", "internalType": "address"}, {"name": "_outcomeIndex", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "transferOwnership", "inputs": [{"name": "new<PERSON>wner", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "OwnershipTransferred", "inputs": [{"name": "previousOwner", "type": "address", "indexed": true, "internalType": "address"}, {"name": "new<PERSON>wner", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "Resolver_PredictionResolved", "inputs": [{"name": "conditionId", "type": "address", "indexed": true, "internalType": "address"}, {"name": "outcomeIndex", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "OwnableInvalidOwner", "inputs": [{"name": "owner", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "OwnableUnauthorizedAccount", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Resolver_AlreadyResolved", "inputs": []}, {"type": "error", "name": "Resolver_FailedToResolve", "inputs": [{"name": "conditionId", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Resolver_InvalidAddress", "inputs": []}, {"type": "error", "name": "Resolver_InvalidDate", "inputs": []}, {"type": "error", "name": "Resolver_InvalidOutcome", "inputs": []}]}