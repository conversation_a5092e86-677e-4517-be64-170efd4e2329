// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { Ownable } from "@openzeppelin/contracts/access/Ownable.sol";
import { IPrediction } from "./interfaces/IPrediction.sol";
import { IFactory } from "./interfaces/IFactory.sol";

contract Resolver is Ownable {
  IFactory s_factory;

  event Resolver_PredictionResolved(address indexed conditionId, uint256 outcomeIndex);

  /// @notice Gas Savings: since Solidity 0.8.4, custom errors in most gas-efficient
  error Resolver_InvalidDate();
  error Resolver_InvalidAddress();
  error Resolver_InvalidOutcome();
  error Resolver_AlreadyResolved();
  error Resolver_FailedToResolve(address conditionId);

  constructor(address _factory) Ownable(msg.sender) {
    if (_factory == address(0)) revert Resolver_InvalidAddress();
    s_factory = IFactory(_factory);
  }

  function resolverRouter(address _prediction, uint256 _outcomeIndex) external onlyOwner {
    if (_prediction == address(0)) revert Resolver_InvalidAddress();

    (
      , // address creator,
      address[] memory resolvers,
      , // bytes32 polygonHash,
      , // string[] memory conditions,
      , // uint256 startDate,
      uint256 endDate,
      uint256 outcomeSlot,
      bool resolved
    ) = s_factory.getPrediction(_prediction);

    if (resolved) revert Resolver_AlreadyResolved();
    if (_outcomeIndex > outcomeSlot) revert Resolver_InvalidOutcome();
    if (block.timestamp < endDate) revert Resolver_InvalidDate();
    bool isResolver = false;
    for (uint256 i = 0; i < resolvers.length; i++) {
        if (msg.sender == resolvers[i]) {
            isResolver = true;
            break;
        }
    }
    if (!isResolver) revert Resolver_InvalidAddress();

    // REVIEW: calls the updatePredictionStatus of the Factory contract here
    try IPrediction(_prediction).setResolved(_outcomeIndex) returns (bool success_) {
      if (!success_) revert Resolver_FailedToResolve(_prediction);
      emit Resolver_PredictionResolved(_prediction, _outcomeIndex);
    } catch  {
      // REVIEW: consider logging specific error reasons for better debugging.
      revert Resolver_FailedToResolve(_prediction);
    }
  }
}