// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { IFactory } from "../interfaces/IFactory.sol";
import { IPrediction } from "../interfaces/IPrediction.sol";

contract View {
    address s_factoryAddress;

    struct FullPredictionInfo {
        address prediction;
        address creator;
        string polygonId;
        string polygonCid;
        string[] conditions;
        uint256 outcomeSlot;
        uint256 startDate;
        uint256 endDate;
        address[] resolvers;
        bool resolved;
        bool active;
        uint256 totalLiquidity;
        uint256[] liquidityPerOutcome;
    }

    error View_InvalidOutcome();

    constructor(address _factory) {
        s_factoryAddress = _factory;
    }

    /**
     * IFactory
    */

    function getPrediction(address _prediction) external view returns (
        address creator_,
        address[] memory resolvers_,
        bytes32 polygonHash_,
        string[] memory conditions_,
        uint256 startDate_,
        uint256 endDate_,
        uint256 outcomeSlot_,
        bool resolved_
    ) {
        IPrediction.Metadata memory meta = IFactory(s_factoryAddress).getPredictionStruct(_prediction);
        return (
            IPrediction(_prediction).getCreator(),
            meta.resolvers,
            IFactory(_prediction).getPolygonHash(_prediction),
            meta.conditions,
            meta.startDate,
            meta.endDate,
            meta.outcomeSlot,
            meta.resolved
        );
    }

    function getPredictionsByPolygon(
        bytes32 _polygonHash
    ) external view returns (IPrediction.Metadata[] memory) {
        return IFactory(s_factoryAddress).getPredictionsByPolygon(_polygonHash);
    }

    function getTotalPredictionsByPolygon(
        bytes32 _polygonHash
    ) external view returns (uint256) {
        return IFactory(s_factoryAddress).getTotalPredictionsByPolygon(_polygonHash);
    }

    function getPredictions(
        uint256 _offset, uint256 _limit
    ) external view returns (IPrediction.Metadata[] memory) {
        return IFactory(s_factoryAddress).getPredictions(_offset, _limit);
    }

    function getTotalPredictions() external view returns (uint256) {
        return IFactory(s_factoryAddress).getTotalPredictions();
    }

    function getResolvablePredictions(
        uint256 _offset, uint256 _limit
    ) external view returns (IPrediction.Metadata[] memory) {
        return IFactory(s_factoryAddress).getResolvablePredictions(_offset, _limit);
    }

    function getPendingResolutions() external view returns (IPrediction.Metadata[] memory) {
        return IFactory(s_factoryAddress).getPendingResolutions();
    }

    /**
     * IPrediction
    */

    function getCreator(address _prediction) external view returns (address) {
        return IPrediction(_prediction).getCreator();
    }

    function getMetadata(
        address _prediction
    ) external view returns (IPrediction.Metadata memory) {
        return IPrediction(_prediction).getMetadata();
    }

    function isActive(address _prediction) external view returns (bool) {
        return IPrediction(_prediction).isActive();
    }

    function getTotalLiquidity(address _prediction) external view returns (uint256) {
        return IPrediction(_prediction).getTotalLiquidity();
    }

    function getLiquidity(
        address _prediction, uint256 _outcomeIndex
    ) external view returns (uint256) {
        IPrediction.Metadata memory meta = IPrediction(_prediction).getMetadata();
        if (_outcomeIndex > meta.outcomeSlot) revert View_InvalidOutcome();
        return IPrediction(_prediction).getLiquidity(_outcomeIndex);
    }

    function getFullPredictionInfo(address _prediction)
        external
        view
        returns (FullPredictionInfo memory info_)
    {
        IPrediction p = IPrediction(_prediction);
        IPrediction.Metadata memory meta = p.getMetadata();

        uint256[] memory outcomeLiquidity = new uint256[](meta.outcomeSlot);
        for (uint256 i = 0; i < meta.outcomeSlot; i++) {
            outcomeLiquidity[i] = p.getLiquidity(i);
        }

        info_ = FullPredictionInfo({
            prediction: _prediction,
            creator: p.getCreator(),
            polygonId: meta.polygonId,
            polygonCid: meta.polygonCid,
            conditions: meta.conditions,
            outcomeSlot: meta.outcomeSlot,
            startDate: meta.startDate,
            endDate: meta.endDate,
            resolvers: meta.resolvers,
            resolved: meta.resolved,
            active: p.isActive(),
            totalLiquidity: p.getTotalLiquidity(),
            liquidityPerOutcome: outcomeLiquidity
        });

        return info_;
    }
}