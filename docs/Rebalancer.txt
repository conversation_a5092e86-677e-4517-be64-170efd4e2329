{"abi": [{"type": "function", "name": "distribution", "inputs": [{"name": "_netCollateral", "type": "uint256", "internalType": "uint256"}, {"name": "_outcomeLiquidity", "type": "uint256", "internalType": "uint256"}, {"name": "_totalLiquidity", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "tokenAmount_", "type": "uint256", "internalType": "uint256"}], "stateMutability": "pure"}, {"type": "error", "name": "Rebalancer_AmountCannotBeZero", "inputs": []}]}