// SPDX-License-Identifier: MIT
pragma solidity 0.8.26;

import { IPrediction } from "./IPrediction.sol";

interface IFactory {

    // REVIEW: add updatePredictionStatus here

    function getPrediction(address _prediction)
    external
    view
    returns (
        address creator_,
        address[] memory resolvers_,
        bytes32 polygonHash_,
        string[] memory conditions_,
        uint256 startDate_,
        uint256 endDate_,
        uint256 outcomeSlot_,
        bool resolved_
    );

    function getPolygonHash(address _prediction) external view returns (bytes32);

    function getPredictionStruct(address _prediction) external view returns (IPrediction.Metadata memory);

    function getPredictionsByPolygon(bytes32 _polygonHash) external view returns (IPrediction.Metadata[] memory);

    function getTotalPredictionsByPolygon(bytes32 _polygonHash) external view returns (uint256);
    
    function getPredictions(uint256 _offset, uint256 _limit) external view returns (IPrediction.Metadata[] memory);

    function getTotalPredictions() external view returns (uint256);

    function getResolvablePredictions(uint256 _offset, uint256 _limit) external view returns (IPrediction.Metadata[] memory);
    
    function getPendingResolutions() external view returns (IPrediction.Metadata[] memory);
}