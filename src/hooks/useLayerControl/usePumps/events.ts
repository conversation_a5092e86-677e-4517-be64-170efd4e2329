import { <PERSON><PERSON><PERSON><PERSON><PERSON>, LineLayer, MapMouseEvent, Map as MapboxMap } from "mapbox-gl";
import mapboxgl from "mapbox-gl";

import { usePumpsManagementStore } from "./store";

const PUMP_LAYER_ID = "pump-tiles-layer";
const PUMP_SOURCE_ID = "pump-tiles";
const PUMP_SOURCE_LAYER = "f_ibi_bond";

const layerExists = (map: MapboxMap, layerId: string): boolean => {
  if (!map) return false;
  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: MapboxMap, sourceId: string): boolean => {
  if (!map) return false;
  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: MapboxMap,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];
    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export const pumpsEventHandler = {
  events: {
    onPumpClick: async (
      e: MapMouseEvent,
      map: MapboxMap,
      currentSelectedId: React.MutableRefObject<string | null>,
      setSelectedPumpId: (id: string | null) => void,
      setIsLoadingPump?: (isLoading: boolean) => void,
      setSelectedPumpDetails?: (details: any) => void,
    ) => {
      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as string;
      const isCtrlPressed = e.originalEvent ? (e.originalEvent as MouseEvent).ctrlKey : false;
      const pumpsStore = usePumpsManagementStore.getState();
      const { addSelectedPump, removeSelectedPump, setMultiSelectMode, selectedPumpsMulti, clearSelectedPumps } =
        pumpsStore;

      if (isCtrlPressed) {
        setMultiSelectMode(true);
        if (selectedPumpsMulti.includes(featureId)) {
          removeSelectedPump(featureId);
          map.setFeatureState(
            { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: featureId },
            { selected: false },
          );
        } else {
          addSelectedPump(featureId);
          map.setFeatureState(
            { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: featureId },
            { selected: true },
          );
        }
        setSelectedPumpId(featureId);
        return;
      } else {
        setMultiSelectMode(false);
        if (selectedPumpsMulti.length > 0) {
          selectedPumpsMulti.forEach((id) => {
            if (id !== featureId) {
              map.setFeatureState({ source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id }, { selected: false });
            }
          });
          clearSelectedPumps();
        }
        if (currentSelectedId.current && currentSelectedId.current !== featureId) {
          map.setFeatureState(
            { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: currentSelectedId.current },
            { selected: false },
          );
        }
        map.setFeatureState(
          { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: featureId },
          { selected: true },
        );
        currentSelectedId.current = featureId;
        setSelectedPumpId(featureId);

        if (setIsLoadingPump && setSelectedPumpDetails) {
          try {
            setIsLoadingPump(true);
            setSelectedPumpDetails({
              id: featureId,
              properties: e.features[0].properties,
            });
          } catch (error) {
            console.error("Failed to fetch pump details:", error);
          } finally {
            setIsLoadingPump(false);
          }
        }
      }
    },
    onBaseMapClick: (e: MapMouseEvent, map: MapboxMap, clearSelection: () => void) => {
      const pumpFeatures = safeQueryRenderedFeatures(map, e.point, {
        layers: [PUMP_LAYER_ID],
      });

      if (pumpFeatures.length === 0) {
        clearSelection();
      }
    },
    onMouseMoveOnPump: (e: MapMouseEvent, map: MapboxMap, currentHoveredId: React.MutableRefObject<string | null>) => {
      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as string;
      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current) {
          map.setFeatureState(
            { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: currentHoveredId.current },
            { hover: false },
          );
        }
        map.setFeatureState({ source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: featureId }, { hover: true });
        currentHoveredId.current = featureId;
      }
      map.getCanvas().style.cursor = "pointer";
    },
    onMouseLeavePump: (map: MapboxMap, currentHoveredId: React.MutableRefObject<string | null>) => {
      if (currentHoveredId.current) {
        map.setFeatureState(
          { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: currentHoveredId.current },
          { hover: false },
        );
        currentHoveredId.current = null;
      }
      map.getCanvas().style.cursor = "";
    },
    clearPumpSelection: (
      map: MapboxMap,
      currentSelectedId: React.MutableRefObject<string | null>,
      setSelectedPumpId: (id: string | null) => void,
    ) => {
      if (currentSelectedId.current) {
        map.setFeatureState(
          { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: currentSelectedId.current },
          { selected: false },
        );
        currentSelectedId.current = null;
        setSelectedPumpId(null);
      }
      map.getCanvas().style.cursor = "";
    },
    clearAllPumpSelections: (
      map: MapboxMap,
      selectedPumpsMulti: string[],
      clearSelectedPumps: () => void,
      setSelectedPumpId: (id: string | null) => void,
      currentSelectedId: React.MutableRefObject<string | null>,
    ) => {
      selectedPumpsMulti.forEach((id) => {
        map.setFeatureState({ source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id }, { selected: false });
      });
      clearSelectedPumps();
      if (currentSelectedId.current) {
        map.setFeatureState(
          { source: PUMP_SOURCE_ID, sourceLayer: PUMP_SOURCE_LAYER, id: currentSelectedId.current },
          { selected: false },
        );
        currentSelectedId.current = null;
      }
      setSelectedPumpId(null);
      map.getCanvas().style.cursor = "";
    },
    removePumpLayers: (
      map: MapboxMap,
      currentHoveredId: React.MutableRefObject<string | null>,
      currentSelectedId: React.MutableRefObject<string | null>,
      onMouseMove: (e: MapMouseEvent) => void,
    ) => {
      try {
        if (layerExists(map, PUMP_LAYER_ID)) {
          map.off("mousemove", PUMP_LAYER_ID, onMouseMove);
          map.off("mouseleave", PUMP_LAYER_ID, () => {});
          map.off("click", PUMP_LAYER_ID, () => {});
        }

        ["pump-tiles-layer", "pump-tiles-border", "pump-tiles-glow"].forEach((layerId) => {
          if (layerExists(map, layerId)) {
            try {
              map.removeLayer(layerId);
            } catch (error) {
              console.error(`Error removing layer ${layerId}:`, error);
            }
          }
        });

        if (sourceExists(map, PUMP_SOURCE_ID)) {
          try {
            map.removeSource(PUMP_SOURCE_ID);
          } catch (error) {
            console.error("Error removing pump-tiles source:", error);
          }
        }

        currentHoveredId.current = null;
        currentSelectedId.current = null;
      } catch (error) {
        console.error("Error in removePumpLayers:", error);
      }
    },
    cleanupPumpLayers: (map: MapboxMap) => {
      if (layerExists(map, PUMP_LAYER_ID)) {
        try {
          map.removeLayer(PUMP_LAYER_ID);
        } catch (error) {
          console.error(`Error removing layer ${PUMP_LAYER_ID}:`, error);
        }
      }
      if (sourceExists(map, PUMP_SOURCE_ID)) {
        try {
          map.removeSource(PUMP_SOURCE_ID);
        } catch (error) {
          console.error(`Error removing source ${PUMP_SOURCE_ID}:`, error);
        }
      }
    },
    safeQueryRenderedFeatures,
  },
  layers: {
    pump_layer: (): FillLayer => {
      return {
        id: PUMP_LAYER_ID,
        type: "fill",
        source: PUMP_SOURCE_ID,
        "source-layer": PUMP_SOURCE_LAYER,
        paint: {
          "fill-color": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            "transparent",
            ["boolean", ["feature-state", "hover"], false],
            "#768FFF",
            "#0288d1",
          ],
          "fill-opacity": [
            "case",
            ["boolean", ["feature-state", "selected"], false],
            0.4,
            ["boolean", ["feature-state", "hover"], false],
            0.6,
            0.4,
          ],
        },
        minzoom: 0,
        maxzoom: 22,
      };
    },
    pump_border_layer: (): LineLayer => {
      return {
        id: "pump-tiles-border",
        type: "line",
        source: PUMP_SOURCE_ID,
        "source-layer": PUMP_SOURCE_LAYER,
        paint: {
          "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
          "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
          "line-opacity": 1,
        },
        minzoom: 0,
        maxzoom: 22,
      };
    },
  },
};
