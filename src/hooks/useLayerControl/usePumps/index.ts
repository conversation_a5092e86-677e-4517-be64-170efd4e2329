import throttle from "lodash/throttle";
import type { LayerSpecification } from "mapbox-gl";
import mapboxgl from "mapbox-gl";
import { useCallback, useEffect, useRef } from "react";

import { usePumpsManagementStore } from "./store";

const layerExists = (map: mapboxgl.Map | null, layerId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getLayer === "function" && map.getLayer(layerId) !== undefined;
  } catch (error) {
    return false;
  }
};

const sourceExists = (map: mapboxgl.Map | null, sourceId: string): boolean => {
  if (!map) return false;

  try {
    return typeof map.getSource === "function" && map.getSource(sourceId) !== undefined;
  } catch (error) {
    return false;
  }
};

const safeQueryRenderedFeatures = (
  map: mapboxgl.Map,
  point: mapboxgl.Point,
  options: { layers: string[] },
): mapboxgl.MapboxGeoJSONFeature[] => {
  try {
    const allLayersExist = options.layers.every((layerId) => layerExists(map, layerId));
    if (!allLayersExist) return [];

    return map.queryRenderedFeatures(point, options);
  } catch (error) {
    console.error("Error querying rendered features:", error);
    return [];
  }
};

export function usePumps(mapRef?: React.RefObject<mapboxgl.Map | null>, mapLoaded?: boolean) {
  const currentHoveredId = useRef<string | null>(null);
  const currentSelectedId = useRef<string | null>(null);
  const {
    setSelectedPumpId,
    clearSelectedPumps,
    setMultiSelectMode,
    setIsLoadingPump,
    setSelectedPumpDetails,
    resetPumpData,
  } = usePumpsManagementStore();

  const PUMPS_MIN_ZOOM = 0;
  const PUMPS_MAX_ZOOM = 22;

  const onMouseMove = useCallback(
    throttle((e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map) return;

      if (!e.features?.length || e.features[0].id === undefined) return;
      const featureId = e.features[0].id as string;

      if (featureId !== currentHoveredId.current) {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "pump-tiles", sourceLayer: "f_ibi_bond", id: currentHoveredId.current },
            { hover: false },
          );
        }

        map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id: featureId }, { hover: true });

        currentHoveredId.current = featureId;
      }

      map.getCanvas().style.cursor = "pointer";
    }, 16),
    [mapRef],
  );

  const clearSelection = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (currentSelectedId.current !== null) {
      map.setFeatureState(
        { source: "pump-tiles", sourceLayer: "f_ibi_bond", id: currentSelectedId.current },
        { selected: false },
      );
      currentSelectedId.current = null;
    }

    const { selectedPumpsMulti } = usePumpsManagementStore.getState();
    selectedPumpsMulti.forEach((id) => {
      map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id }, { selected: false });
    });

    clearSelectedPumps();
    setSelectedPumpId(null);
    setMultiSelectMode(false);
    resetPumpData();
  }, [mapRef, clearSelectedPumps, setSelectedPumpId, setMultiSelectMode, resetPumpData]);

  const onPumpClick = useCallback(
    async (e: mapboxgl.MapMouseEvent & { features?: mapboxgl.MapboxGeoJSONFeature[] }) => {
      const map = mapRef?.current;
      if (!map || !e.features?.length || e.features[0].id === undefined) return;

      const featureId = e.features[0].id as string;
      const isCtrlPressed = e.originalEvent ? (e.originalEvent as MouseEvent).ctrlKey : false;
      const pumpsStore = usePumpsManagementStore.getState();
      const {
        addSelectedPump,
        removeSelectedPump,
        setMultiSelectMode,
        selectedPumpsMulti,
        clearSelectedPumps,
        setSelectedPumpId,
      } = pumpsStore;

      if (isCtrlPressed) {
        setMultiSelectMode(true);
        if (selectedPumpsMulti.includes(featureId)) {
          removeSelectedPump(featureId);
          map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id: featureId }, { selected: false });
        } else {
          addSelectedPump(featureId);
          map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id: featureId }, { selected: true });
        }
        setSelectedPumpId(featureId);
        return;
      } else {
        setMultiSelectMode(false);
        if (selectedPumpsMulti.length > 0) {
          selectedPumpsMulti.forEach((id) => {
            if (id !== featureId) {
              map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id }, { selected: false });
            }
          });
          clearSelectedPumps();
        }
        if (currentSelectedId.current && currentSelectedId.current !== featureId) {
          map.setFeatureState(
            { source: "pump-tiles", sourceLayer: "f_ibi_bond", id: currentSelectedId.current },
            { selected: false },
          );
        }
        map.setFeatureState({ source: "pump-tiles", sourceLayer: "f_ibi_bond", id: featureId }, { selected: true });
        currentSelectedId.current = featureId;
        setSelectedPumpId(featureId);

        if (e.features[0].geometry.type === "Polygon" || e.features[0].geometry.type === "MultiPolygon") {
          const bounds = new mapboxgl.LngLatBounds();

          const coordinates =
            e.features[0].geometry.type === "Polygon"
              ? e.features[0].geometry.coordinates[0]
              : e.features[0].geometry.coordinates.flat(1);

          if (coordinates && coordinates.length) {
            coordinates.forEach((coord: any) => {
              if (Array.isArray(coord) && coord.length >= 2) {
                bounds.extend([coord[0], coord[1]]);
              }
            });
          }

          if (!bounds.isEmpty()) {
            const center = bounds.getCenter();
            map.flyTo({
              center: [center.lng, center.lat],
              zoom: map.getZoom(),
              animate: true,
              duration: 500,
              essential: true,
            });
          }
        } else if (e.lngLat) {
          map.flyTo({
            center: [e.lngLat.lng, e.lngLat.lat],
            zoom: map.getZoom(),
            essential: true,
            animate: true,
            duration: 500,
          });
        }

        try {
          setIsLoadingPump(true);
          setSelectedPumpDetails({
            id: featureId,
            properties: e.features[0].properties,
          });
        } catch (error) {
          console.error("Failed to fetch pump details:", error);
        } finally {
          setIsLoadingPump(false);
        }
      }
    },
    [mapRef, setIsLoadingPump, setSelectedPumpDetails],
  );

  const cleanupLayers = useCallback(() => {
    const map = mapRef?.current;
    if (!map) return;

    if (layerExists(map, "pump-tiles-layer")) {
      map.off("mousemove", "pump-tiles-layer", onMouseMove);
      map.off("mouseleave", "pump-tiles-layer", () => {});
      map.off("click", "pump-tiles-layer", onPumpClick);
    }

    ["pump-tiles-layer", "pump-tiles-border", "pump-tiles-glow"].forEach((layerId) => {
      if (layerExists(map, layerId)) {
        try {
          map.removeLayer(layerId);
        } catch (error) {
          console.error(`Error removing layer ${layerId}:`, error);
        }
      }
    });

    if (sourceExists(map, "pump-tiles")) {
      try {
        map.removeSource("pump-tiles");
      } catch (error) {
        console.error("Error removing pump-tiles source:", error);
      }
    }

    currentHoveredId.current = null;
    currentSelectedId.current = null;
  }, [mapRef, onMouseMove, onPumpClick]);

  const setupPumpsLayer = useCallback(() => {
    const map = mapRef?.current;
    if (!map || !mapLoaded) return;

    cleanupLayers();

    try {
      if (!sourceExists(map, "pump-tiles")) {
        map.addSource("pump-tiles", {
          type: "vector",
          tiles: ["https://tiles.yby.energy/dev/pump/{z}/{x}/{y}"],
          minzoom: PUMPS_MIN_ZOOM,
          maxzoom: PUMPS_MAX_ZOOM,
        });
      }

      const layersToAdd: LayerSpecification[] = [];

      if (!layerExists(map, "pump-tiles-layer")) {
        layersToAdd.push({
          id: "pump-tiles-layer",
          type: "fill",
          source: "pump-tiles",
          "source-layer": "f_ibi_bond",
          minzoom: PUMPS_MIN_ZOOM,
          maxzoom: PUMPS_MAX_ZOOM,
          paint: {
            "fill-color": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              "transparent",
              ["boolean", ["feature-state", "hover"], false],
              "#768FFF",
              "#0288d1",
            ],
            "fill-opacity": [
              "case",
              ["boolean", ["feature-state", "selected"], false],
              0.4,
              ["boolean", ["feature-state", "hover"], false],
              0.6,
              0.4,
            ],
          },
        });
      }

      if (!layerExists(map, "pump-tiles-border")) {
        layersToAdd.push({
          id: "pump-tiles-border",
          type: "line",
          source: "pump-tiles",
          "source-layer": "f_ibi_bond",
          minzoom: PUMPS_MIN_ZOOM,
          maxzoom: PUMPS_MAX_ZOOM,
          paint: {
            "line-color": ["case", ["boolean", ["feature-state", "selected"], false], "#0066ff", "#03a9f4"],
            "line-width": ["case", ["boolean", ["feature-state", "selected"], false], 3, 1],
            "line-opacity": 1,
          },
        });
      }

      layersToAdd.forEach((layer) => {
        if (layer && layer.id) {
          try {
            map.addLayer(layer);
          } catch (error) {
            console.error(`Error adding layer ${layer.id}:`, error);
          }
        }
      });

      map.on("mousemove", "pump-tiles-layer", onMouseMove);

      map.on("mouseleave", "pump-tiles-layer", () => {
        if (currentHoveredId.current !== null) {
          map.setFeatureState(
            { source: "pump-tiles", sourceLayer: "f_ibi_bond", id: currentHoveredId.current },
            { hover: false },
          );
          currentHoveredId.current = null;
        }
        map.getCanvas().style.cursor = "";
      });

      map.on("click", "pump-tiles-layer", onPumpClick);

      map.on("click", (e) => {
        const pumpFeatures = safeQueryRenderedFeatures(map, e.point, {
          layers: ["pump-tiles-layer"],
        });

        if (pumpFeatures.length === 0) {
          clearSelection();
        }
      });
    } catch (error) {
      console.error("Error setting up pump layer:", error);
    }
  }, [mapRef, mapLoaded, cleanupLayers, clearSelection, onMouseMove, onPumpClick]);

  useEffect(() => {
    if (mapLoaded) {
      setupPumpsLayer();
    }
  }, [mapLoaded, setupPumpsLayer]);

  useEffect(() => {
    return () => {
      resetPumpData();
      cleanupLayers();
    };
  }, [resetPumpData, cleanupLayers]);

  return {
    setupPumpsLayer,
    cleanupLayers,
    onMouseMove,
    clearSelection,
  };
}
