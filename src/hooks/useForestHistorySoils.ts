import { useMapSelector } from "@/stores/map.store";
import { ForestHistorySoils } from "@/types";
import { useCallback } from "react";

import usePrivyAuth from "./usePrivyAuth";

export const useForestHistorySoils = () => {
  const { setForestHistorySoils, setIsLoadingForestHistorySoils, setForestHistorySoilsError } = useMapSelector(
    (state) => ({
      setForestHistorySoils: state.setForestHistorySoils,
      setIsLoadingForestHistorySoils: state.setIsLoadingForestHistorySoils,
      setForestHistorySoilsError: state.setForestHistorySoilsError,
    }),
  );

  const { getJWT } = usePrivyAuth();

  const fetchForestHistorySoils = useCallback(
    async (id: string) => {
      if (!id) return;

      try {
        setIsLoadingForestHistorySoils(true);
        setForestHistorySoilsError(null);

        const jwt = await getJWT();
        const response = await fetch(`https://api.yby.energy/api/soils/forest-history?soilsId=${id}`, {
          headers: {
            Authorization: `Bearer ${jwt}`,
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch soils forest history");
        }

        const soilsData = await response.json();

        const transformedData: ForestHistorySoils = {
          id: soilsData.id,
          soilsId: soilsData.soilsId,
          forestHistory: soilsData.forestHistory,
          areaHa: soilsData.areaHa,
          createdAt: soilsData.createdAt,
          updatedAt: soilsData.updatedAt,
          ecoregions: soilsData.ecoregions,
        };

        setForestHistorySoils(transformedData);
      } catch (err) {
        console.error("Error fetching soils forest history:", err);
        setForestHistorySoilsError(err instanceof Error ? err.message : "Failed to fetch soils forest history");
        setForestHistorySoils(null);
      } finally {
        setIsLoadingForestHistorySoils(false);
      }
    },
    [getJWT, setForestHistorySoils, setForestHistorySoilsError, setIsLoadingForestHistorySoils],
  );

  return {
    fetchForestHistorySoils,
  };
};
