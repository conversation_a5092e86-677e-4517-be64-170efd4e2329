import IbiIcon from "@/components/IbiUi/IbiIcon";
import MapLayers from "@/components/IbiUi/IbiMap/MapLayers";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useBaseMap } from "@/hooks/useBaseMap";
import { useDetectLocation } from "@/hooks/useDetectLocation";
import { useLayerControl } from "@/hooks/useLayerControl";
import { useAnimals, useAnimalsManagementStore } from "@/hooks/useLayerControl/useAnimals";
import useMap from "@/hooks/useMap";
import { cn } from "@/lib/utils";
import { useMapSelector } from "@/stores/map.store";
import { CurrentBaseMap } from "@/types";
import { MAPBOX_TOKEN, MAP_PROJECTION, TERRAIN_EXAGGERATION } from "@/utils/constants";
import "mapbox-gl/dist/mapbox-gl.css";
import { useCallback, useEffect, useRef, useState } from "react";
import { Map } from "react-map-gl";

import MapFooterToolbar from "../MapFooterToolbar";

const animalClasses = ["AMPHIBIA", "AVES", "MAMMALIA", "REPTILIA"];

const familiesByClass = {
  AMPHIBIA: ["BUFONIDAE", "HYLIDAE", "RANIDAE", "LEPTODACTYLIDAE"],
  AVES: ["ACCIPITRIDAE", "ANATIDAE", "EMBERIZIDAE", "CICONIIDAE"],
  MAMMALIA: ["FELIDAE", "CANIDAE", "BOVIDAE", "CERVIDAE"],
  REPTILIA: ["GEKKONIDAE", "IGUANIDAE", "CHELONIIDAE"],
};

const SpeciesFilterPanel = () => {
  const { selectedSpeciesFilter, setSelectedSpeciesFilter, filterBy, setFilterBy } = useAnimalsManagementStore();

  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const [showFilter, setShowFilter] = useState(false);

  const filterOptions = [
    { value: "class" as const, label: "Class" },
    { value: "family" as const, label: "Family" },
    { value: "genus" as const, label: "Genus" },
    { value: "sci_name" as const, label: "Scientific Name" },
  ];

  const handleSpeciesSelect = (species: string) => {
    if (selectedSpeciesFilter === species) {
      setSelectedSpeciesFilter(null);
    } else {
      setSelectedSpeciesFilter(species);
    }
  };

  const handleClassSelect = (classKey: string) => {
    if (selectedClass === classKey) {
      setSelectedClass(null);
    } else {
      setSelectedClass(classKey);
    }
  };

  const clearFilter = () => {
    setSelectedSpeciesFilter(null);
    setSelectedClass(null);
  };

  return (
    <div className="absolute top-[60px] sm:top-[10px] left-0 mt-[200px] ml-[75px] flex flex-col gap-2">
      <Popover open={showFilter} onOpenChange={setShowFilter}>
        <PopoverTrigger asChild>
          <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-black/80 backdrop-blur-md flex items-center justify-center px-2 gap-2">
            <IbiIcon icon="material-symbols:filter-list" className="text-xl" />
            Filter Species
            {selectedSpeciesFilter && <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>}
          </button>
        </PopoverTrigger>
        <PopoverContent
          sideOffset={12}
          side="right"
          align="start"
          className="w-[350px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
        >
          <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
            <div className="flex items-center justify-between mb-4">
              <h1 className="text-gray-400 text-[12px]">Filter by Species</h1>
              {selectedSpeciesFilter && (
                <button
                  onClick={clearFilter}
                  className="text-xs text-gray-400 hover:text-white px-2 py-1 bg-gray-700 rounded"
                >
                  Clear
                </button>
              )}
            </div>

            {/* Filter Type Selector */}
            <div className="mb-4">
              <h2 className="text-gray-400 text-[10px] mb-2">FILTER BY</h2>
              <div className="grid grid-cols-2 gap-1">
                {filterOptions.map((option) => (
                  <button
                    key={option.value}
                    onClick={() => setFilterBy(option.value)}
                    className={cn(
                      "p-2 rounded text-xs",
                      filterBy === option.value
                        ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                        : "hover:bg-[#ffffff15] text-gray-300",
                    )}
                  >
                    {option.label}
                  </button>
                ))}
              </div>
            </div>

            {/* Class Selection */}
            {filterBy === "class" && (
              <div className="space-y-2">
                <h2 className="text-gray-400 text-[10px]">ANIMAL CLASSES</h2>
                {animalClasses.map((className) => (
                  <button
                    key={className}
                    onClick={() => handleSpeciesSelect(className)}
                    className={cn(
                      "w-full p-2 rounded text-left text-sm",
                      selectedSpeciesFilter === className
                        ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                        : "hover:bg-[#ffffff15] text-gray-300",
                    )}
                  >
                    {className}
                  </button>
                ))}
              </div>
            )}

            {/* Family Selection */}
            {filterBy === "family" && (
              <div className="space-y-2">
                <h2 className="text-gray-400 text-[10px]">SELECT CLASS FIRST</h2>
                {animalClasses.map((className) => (
                  <div key={className}>
                    <button
                      onClick={() => handleClassSelect(className)}
                      className={cn(
                        "w-full flex items-center justify-between px-3 py-2 rounded-md text-sm font-medium transition-all",
                        selectedClass === className
                          ? "bg-blue-500/20 text-blue-400 border border-blue-500/30"
                          : "text-white/70 hover:text-white hover:bg-[#ffffff0a]",
                      )}
                    >
                      {className}
                      <IbiIcon
                        icon={
                          selectedClass === className ? "material-symbols:expand-less" : "material-symbols:expand-more"
                        }
                        className="w-4 h-4"
                      />
                    </button>

                    {selectedClass === className && (
                      <div className="ml-4 mt-1 space-y-1">
                        {familiesByClass[className as keyof typeof familiesByClass]?.map((family) => (
                          <button
                            key={family}
                            onClick={() => handleSpeciesSelect(family)}
                            className={cn(
                              "w-full text-left px-3 py-2 rounded-md text-sm transition-all",
                              selectedSpeciesFilter === family
                                ? "bg-yellow-500/20 text-yellow-400 border border-yellow-500/30"
                                : "text-white/60 hover:text-white hover:bg-[#ffffff0a]",
                            )}
                          >
                            {family}
                          </button>
                        ))}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            )}

            {/* Manual Input for Genus and Scientific Name */}
            {(filterBy === "genus" || filterBy === "sci_name") && (
              <div className="space-y-2">
                <h2 className="text-gray-400 text-[10px]">
                  ENTER {filterBy === "genus" ? "GENUS" : "SCIENTIFIC NAME"}
                </h2>
                <input
                  type="text"
                  placeholder={`Enter ${filterBy === "genus" ? "genus" : "scientific name"}...`}
                  value={selectedSpeciesFilter || ""}
                  onChange={(e) => setSelectedSpeciesFilter(e.target.value || null)}
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-sm text-white placeholder-gray-400 focus:border-yellow-400 focus:outline-none"
                />
                <p className="text-xs text-gray-500">
                  {filterBy === "genus" ? "Example: Panthera, Canis, Bufo" : "Example: Panthera leo, Canis lupus"}
                </p>
              </div>
            )}

            {/* Current Filter Display */}
            {selectedSpeciesFilter && (
              <div className="mt-4 p-3 bg-gray-800 rounded">
                <p className="text-xs text-gray-400">Current Filter:</p>
                <p className="text-sm text-yellow-400">
                  {filterBy}: {selectedSpeciesFilter}
                </p>
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

const AnimalsMap = () => {
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const { currentBaseMap, setCurrentBaseMap } = useMap();
  const { mapStyle } = useBaseMap();
  const padding = useMapSelector.use.padding();
  const { latitude, longitude } = useDetectLocation();

  const [mapLoaded, setMapLoaded] = useState(false);
  const [mapInitialized, setMapInitialized] = useState(false);
  const [animalsInitialized, setAnimalsInitialized] = useState(false);

  const { handleMapLoad: layerControlMapLoad } = useLayerControl();

  const defaultViewState = {
    latitude: -14.72,
    longitude: -51.419,
    zoom: 8,
  };

  const [viewState, setViewState] = useState(defaultViewState);

  const { setupAnimalsLayer, cleanupLayers } = useAnimals(mapRef, mapLoaded);

  const baseMapOptions = [
    {
      id: 1,
      label: "Gray Scale",
      mapState: "dark" as CurrentBaseMap,
    },
    {
      id: 2,
      label: "Streets",
      mapState: "streets" as CurrentBaseMap,
    },
    {
      id: 3,
      label: "Outdoors",
      mapState: "outdoors" as CurrentBaseMap,
    },
    {
      id: 4,
      label: "Light",
      mapState: "light" as CurrentBaseMap,
    },
    {
      id: 5,
      label: "Navigation Day",
      mapState: "navigation-day" as CurrentBaseMap,
    },
    {
      id: 6,
      label: "Navigation Night",
      mapState: "navigation-night" as CurrentBaseMap,
    },
  ];

  useEffect(() => {
    if (latitude && longitude && !mapInitialized) {
      setViewState({
        latitude,
        longitude,
        zoom: 8,
      });
      setMapInitialized(true);
    }
  }, [latitude, longitude, mapInitialized]);

  const handleMapLoad = useCallback(
    (event: any) => {
      mapRef.current = event.target;
      setMapLoaded(true);

      layerControlMapLoad(event);

      if (latitude && longitude && mapRef.current) {
        mapRef.current.flyTo({
          center: [longitude, latitude],
          zoom: 8,
          essential: true,
          duration: 1000,
        });
      }

      const map = event.target;
      if (!map.getSource("mapbox-dem")) {
        map.addSource("mapbox-dem", {
          type: "raster-dem",
          url: "mapbox://mapbox.mapbox-terrain-dem-v1",
          tileSize: 512,
          maxzoom: 22,
        });
      }
    },
    [latitude, longitude, layerControlMapLoad],
  );

  useEffect(() => {
    if (mapLoaded && !animalsInitialized) {
      setupAnimalsLayer();
      setAnimalsInitialized(true);
    }
    return () => {
      cleanupLayers();
    };
  }, [mapLoaded, setupAnimalsLayer, cleanupLayers, animalsInitialized]);

  useEffect(() => {
    if (!mapRef?.current || !mapLoaded) return;

    setTimeout(() => {
      cleanupLayers();
      setupAnimalsLayer();
    }, 500);
  }, [currentBaseMap, mapRef, mapLoaded, cleanupLayers, setupAnimalsLayer]);

  return (
    <div className="w-full h-full flex flex-col relative">
      <Map
        ref={mapRef as any}
        mapStyle={mapStyle}
        initialViewState={viewState}
        padding={padding}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={MAP_PROJECTION}
        onLoad={handleMapLoad}
        style={{ width: "100%", height: "100%" }}
        renderWorldCopies={false}
        attributionControl={false}
        preserveDrawingBuffer={false}
        antialias={false}
        terrain={{ source: "mapbox-dem", exaggeration: TERRAIN_EXAGGERATION }}
        pitchWithRotate={true}
      />

      {/* Map Layers Component */}
      <div className="absolute top-[60px] sm:top-[10px] left-0 mt-[100px] ml-[75px] flex flex-col gap-2">
        <MapLayers />
      </div>

      {/* Species Filter Component */}
      <SpeciesFilterPanel />

      {/* Base Map Component */}
      <div className="absolute top-[60px] sm:top-[10px] left-0 mt-[150px] ml-[75px] flex flex-col gap-2">
        <Popover>
          <PopoverTrigger asChild>
            <button className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-black/80 backdrop-blur-md flex items-center justify-center px-2 gap-2">
              <IbiIcon icon="mi:map" className="text-xl" />
              Base Map
            </button>
          </PopoverTrigger>
          <PopoverContent
            sideOffset={12}
            side="right"
            align="start"
            className="w-[298px] rounded-none border-none p-[1px] bg-[#ffffff2f] layers-box-clip-path relative text-white"
          >
            <div className="layers-box-clip-path bg-[#0A0E15] size-full px-4 pb-6 pt-5">
              <h1 className="text-gray-400 text-[12px]">Base Map</h1>
              <div className="mt-3 flex flex-col gap-2">
                <div
                  className={cn(
                    "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                    currentBaseMap === "default" && "bg-[#ffffff2f]",
                  )}
                  onClick={() => {
                    setCurrentBaseMap("default");
                  }}
                >
                  <h2 className="text-gray-300 text-[14px]">Default</h2>
                </div>

                {baseMapOptions.map((option) => (
                  <div
                    key={option.id}
                    className={cn(
                      "p-2 rounded cursor-pointer hover:bg-[#ffffff15]",
                      currentBaseMap === option.mapState && "bg-[#ffffff2f]",
                    )}
                    onClick={() => {
                      setCurrentBaseMap(option.mapState);
                    }}
                  >
                    <h2 className="text-gray-300 text-[14px]">{option.label}</h2>
                  </div>
                ))}
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
      <MapFooterToolbar className="absolute bottom-0 left-0 right-0 z-20" />
    </div>
  );
};

export default AnimalsMap;
