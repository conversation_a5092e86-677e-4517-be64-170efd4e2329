"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { handleStorage } from "@/utils/storage";
import { usePathname, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import EntryLoading from "../EntryLoading";
import { useAppHeaderSelector } from "../Headers/EntryHeader/header.store";
import IbiConfirmDialog from "../IbiUi/IbiConfirmDialog";

// const BackgroundBeams = dynamic(() => import("../ui/beans-bg"));

const AppWrapper = ({ children }: { children: React.ReactNode }) => {
  const { ready = false, authenticated, onLogin, confirmLogout: setConfirmLogout } = usePrivyAuth();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const confirmLogout = useAppHeaderSelector.use.confirmLogout();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  // Handle referral code extraction and auto-login for unauthenticated users
  useEffect(() => {
    if (ready) {
      // Extract referral codes from URL parameters
      const referralCode = searchParams?.get("referral") || searchParams?.get("rc");
      const ibicode = pathname.startsWith("/p/") ? pathname.split("/p/")[1] : null;

      // Store referral codes in localStorage/sessionStorage
      if (referralCode) {
        handleStorage("local", "inviteCode", "create", referralCode);
      }
      if (ibicode) {
        handleStorage("session", "ibiCode", "create", ibicode);
      }

      // Store current path for redirect after login
      const pathHistory = `${pathname}${searchParams ? `?${searchParams}` : ""}`;
      handleStorage("session", "pathHistory", "create", pathHistory);

      // Auto-trigger login for unauthenticated users
      if (!authenticated) {
        onLogin();
      }
    }
  }, [ready, authenticated, onLogin, pathname, searchParams]);

  return (
    <EntryLoading wrapperClass="h-full" condition={ready}>
      <>
        <div className="w-full h-full flex bg-primary-dark min-h-screen overflow-y-hidden overflow-x-hidden">
          <div className="flex-1 h-full">{children}</div>
        </div>
        <IbiConfirmDialog
          title="Confirm logout"
          description="Are you sure you want to log out?"
          open={confirmLogout}
          onOpenChange={toggleConfirmLogout}
          onCancel={toggleConfirmLogout}
          onConfirm={() => {
            setConfirmLogout(true);
            toggleConfirmLogout();
          }}
        />
      </>
    </EntryLoading>
  );
};

export default AppWrapper;
