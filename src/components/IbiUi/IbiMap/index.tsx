"use client";

import { useDetectLocation } from "@/hooks/useDetectLocation";
import { useIbiMap } from "@/hooks/useIbiMap";
import { useMapSelector } from "@/stores/map.store";
import { DEFAULT_MAP_ZOOM, MAPBOX_TOKEN, MAP_PROJECTION, TERRAIN_EXAGGERATION } from "@/utils/constants";
import "mapbox-gl/dist/mapbox-gl.css";
import { useEffect, useState } from "react";
import { Map } from "react-map-gl";

import MapFooterToolbar from "../../MapFooterToolbar";

export default function IbiMap() {
  const { mapRef, handleMapLoad, mapStyle } = useIbiMap();
  const padding = useMapSelector.use.padding();
  const { latitude, longitude } = useDetectLocation();

  const [mapInitialized, setMapInitialized] = useState(false);

  const defaultViewState = {
    latitude: -14.72,
    longitude: -51.419,
    zoom: 8,
  };

  const [viewState, setViewState] = useState(defaultViewState);

  useEffect(() => {
    if (latitude && longitude && !mapInitialized) {
      setViewState({
        latitude,
        longitude,
        zoom: 8,
      });
      setMapInitialized(true);
    }
  }, [latitude, longitude, mapInitialized]);

  const handleLoad = (event: any) => {
    handleMapLoad(event);

    if (latitude && longitude && mapRef?.current) {
      mapRef.current.flyTo({
        center: [longitude, latitude],
        zoom: DEFAULT_MAP_ZOOM,
        essential: true,
      });
    }

    const map = event.target;
    if (!map.getSource("mapbox-dem")) {
      map.addSource("mapbox-dem", {
        type: "raster-dem",
        url: "mapbox://mapbox.mapbox-terrain-dem-v1",
        tileSize: 512,
        maxzoom: 22,
      });
    }
  };

  return (
    <div className="w-full h-full flex flex-col relative">
      <Map
        ref={mapRef as any}
        mapStyle={mapStyle}
        initialViewState={viewState}
        padding={padding}
        mapboxAccessToken={MAPBOX_TOKEN}
        projection={MAP_PROJECTION}
        onLoad={handleLoad}
        interactiveLayerIds={["properties"]}
        style={{
          width: "100%",
          height: "100%",
        }}
        renderWorldCopies={false}
        attributionControl={false}
        preserveDrawingBuffer={false}
        antialias={false}
        terrain={{ source: "mapbox-dem", exaggeration: TERRAIN_EXAGGERATION }}
        pitchWithRotate={true}
      />
      <div
        className="absolute top-0 left-0 w-full h-full pointer-events-none"
        style={{
          background:
            "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, rgba(0,0,0,0.1) 15%, rgba(0,0,0,0.1) 85%, rgba(0,0,0,0.9) 100%)",
          zIndex: 10,
        }}
      />
      <MapFooterToolbar className="absolute bottom-0 left-0 right-0 z-20" />
    </div>
  );
}
