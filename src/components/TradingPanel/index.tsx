"use client";

import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ChevronDown, ChevronUp, TrendingDown, TrendingUp } from "lucide-react";
import React, { useState } from "react";

interface TradeData {
  id: string;
  type: "buy" | "sell";
  age: string;
  mc: string;
  amount: number;
  totalUSD: number;
  trader: string;
}

interface DevTokenData {
  id: string;
  token: string;
  icon: string;
  timeAgo: string;
  migrated: boolean;
  marketCap: number;
  liquidity: number;
  volume1h: number;
}

interface TraderData {
  id: string;
  wallet: string;
  solBalance: number;
  boughtAmount: number;
  boughtAvg: number;
  boughtTotal: number;
  soldAmount: number;
  soldAvg: number;
  soldTotal: number;
  pnl: number;
  remaining: number;
  remainingPercent: number;
  hasSpecialIcons?: boolean[];
}

interface HolderData {
  id: string;
  wallet: string;
  solBalance: number;
  bought: number;
  avgBuy: number;
  sold: number;
  avgSell: number;
  hasInsightX: boolean;
  hasBubblemaps: boolean;
  hasCabalSpy: boolean;
}

interface PositionData {
  id: string;
  token: string;
  bought: number;
  sold: number;
  remaining: number;
  pnl: number;
  pnlPercent: number;
  suggestedAction?: "buy" | "sell";
}

interface OrderData {
  id: string;
  token: string;
  type: "buy" | "sell";
  amount: number;
  currentMC: string;
  targetMC: string;
}

interface TradingPanelProps {
  className?: string;
}

const TradingPanel: React.FC<TradingPanelProps> = ({ className = "" }) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const tradesData: TradeData[] = [
    {
      id: "T001",
      type: "buy",
      age: "11s",
      mc: "$235M",
      amount: 126.5,
      totalUSD: 29.81,
      trader: "AYf...zj6",
    },
    {
      id: "T002",
      type: "buy",
      age: "15s",
      mc: "$235M",
      amount: 37.96,
      totalUSD: 8.94,
      trader: "47v...b6T",
    },
    {
      id: "T003",
      type: "sell",
      age: "20s",
      mc: "$234M",
      amount: 256.4,
      totalUSD: 60.09,
      trader: "E4S...tWw",
    },
    {
      id: "T004",
      type: "sell",
      age: "21s",
      mc: "$234M",
      amount: 7.56,
      totalUSD: 1.77,
      trader: "CWa...s3n",
    },
    {
      id: "T005",
      type: "sell",
      age: "1m",
      mc: "$235M",
      amount: 1.24,
      totalUSD: 290.2,
      trader: "CWa...s3n",
    },
    {
      id: "T006",
      type: "buy",
      age: "3m",
      mc: "$236M",
      amount: 209.9,
      totalUSD: 49.55,
      trader: "Aps...fn9",
    },
    {
      id: "T007",
      type: "sell",
      age: "4m",
      mc: "$234M",
      amount: 71.03,
      totalUSD: 16.67,
      trader: "Pro...c5u",
    },
  ];

  const devTokensData: DevTokenData[] = [
    {
      id: "DT001",
      token: "WIN",
      icon: "🏆",
      timeAgo: "18m ago",
      migrated: false,
      marketCap: 31000,
      liquidity: 4590,
      volume1h: 3810,
    },
    {
      id: "DT002",
      token: "UsELeSS",
      icon: "💎",
      timeAgo: "1h ago",
      migrated: false,
      marketCap: 6590,
      liquidity: 5650,
      volume1h: 11100,
    },
    {
      id: "DT003",
      token: "CIS",
      icon: "🔮",
      timeAgo: "4h ago",
      migrated: true,
      marketCap: 1560,
      liquidity: 1270,
      volume1h: 1905,
    },
    {
      id: "DT004",
      token: "stonkcoins",
      icon: "📈",
      timeAgo: "4h ago",
      migrated: false,
      marketCap: 3000,
      liquidity: 4610,
      volume1h: 0,
    },
    {
      id: "DT005",
      token: "DARKCHEEMS",
      icon: "🐕",
      timeAgo: "5h ago",
      migrated: false,
      marketCap: 4470,
      liquidity: 4650,
      volume1h: 0,
    },
    {
      id: "DT006",
      token: "JUIPALIIRA",
      icon: "🌊",
      timeAgo: "6h ago",
      migrated: false,
      marketCap: 3260,
      liquidity: 4800,
      volume1h: 0,
    },
  ];

  const tradersData: TraderData[] = [
    {
      id: "TR001",
      wallet: "FhRiiV...8tTK",
      solBalance: 2.814,
      boughtAmount: 167000,
      boughtAvg: 23.5,
      boughtTotal: 71.1,
      soldAmount: 118000,
      soldAvg: 86.6,
      soldTotal: 13.6,
      pnl: 118000,
      remaining: 396.5,
      remainingPercent: 0.032,
      hasSpecialIcons: [true, true, true],
    },
    {
      id: "TR002",
      wallet: "8deJ9x...XhU6",
      solBalance: 898.7,
      boughtAmount: 162000,
      boughtAvg: 8.05,
      boughtTotal: 20.1,
      soldAmount: 851000,
      soldAvg: 60.8,
      soldTotal: 14,
      pnl: 689000,
      remaining: 1.44,
      remainingPercent: 0.613,
      hasSpecialIcons: [false, true, false],
    },
    {
      id: "TR003",
      wallet: "j1oAbx...txTF",
      solBalance: 359.5,
      boughtAmount: 908000,
      boughtAvg: 43.1,
      boughtTotal: 21.1,
      soldAmount: 156000,
      soldAvg: 49.4,
      soldTotal: 31.6,
      pnl: 655000,
      remaining: 11000,
      remainingPercent: 0.25,
      hasSpecialIcons: [true, false, true],
    },
    {
      id: "TR004",
      wallet: "jioxqt...nkHX",
      solBalance: 357.7,
      boughtAmount: 675000,
      boughtAvg: 23.8,
      boughtTotal: 28.3,
      soldAmount: 113000,
      soldAvg: 58.8,
      soldTotal: 19.1,
      pnl: 451000,
      remaining: 4.08,
      remainingPercent: 0.02,
      hasSpecialIcons: [true, false, true],
    },
    {
      id: "TR005",
      wallet: "Et6DgJ...FbNu",
      solBalance: 0.845,
      boughtAmount: 441000,
      boughtAvg: 17.7,
      boughtTotal: 24.9,
      soldAmount: 873000,
      soldAvg: 40.9,
      soldTotal: 21.3,
      pnl: 432000,
      remaining: 792000,
      remainingPercent: 0.338,
    },
    {
      id: "TR006",
      wallet: "Jn38n7...624k",
      solBalance: 0,
      boughtAmount: 177000,
      boughtAvg: 27.6,
      boughtTotal: 6.42,
      soldAmount: 215000,
      soldAvg: 30.5,
      soldTotal: 7.05,
      pnl: 381000,
      remaining: 0,
      remainingPercent: 0,
    },
  ];

  const holdersData: HolderData[] = [
    {
      id: "H001",
      wallet: "584apK...QRVU",
      solBalance: 0.199,
      bought: 0,
      avgBuy: 0,
      sold: 0,
      avgSell: 0,
      hasInsightX: true,
      hasBubblemaps: false,
      hasCabalSpy: true,
    },
    {
      id: "H002",
      wallet: "5M8ACG...OW3Y",
      solBalance: 2.08,
      bought: 128000,
      avgBuy: 9.18,
      sold: 0,
      avgSell: 0,
      hasInsightX: true,
      hasBubblemaps: true,
      hasCabalSpy: false,
    },
    {
      id: "H003",
      wallet: "61rVn8...v7Nu",
      solBalance: 29.05,
      bought: 0,
      avgBuy: 0,
      sold: 0,
      avgSell: 0,
      hasInsightX: false,
      hasBubblemaps: false,
      hasCabalSpy: true,
    },
    {
      id: "H004",
      wallet: "8EJEX...A44k",
      solBalance: 1.992,
      bought: 0,
      avgBuy: 0,
      sold: 0,
      avgSell: 0,
      hasInsightX: true,
      hasBubblemaps: false,
      hasCabalSpy: false,
    },
    {
      id: "H005",
      wallet: "8yM8F9...5PQj",
      solBalance: 0.025,
      bought: 0,
      avgBuy: 0,
      sold: 537600,
      avgSell: 16.8,
      hasInsightX: false,
      hasBubblemaps: true,
      hasCabalSpy: true,
    },
    {
      id: "H006",
      wallet: "GbnYDF...d3Ri",
      solBalance: 0.256,
      bought: 93400,
      avgBuy: 13,
      sold: 0,
      avgSell: 0,
      hasInsightX: true,
      hasBubblemaps: false,
      hasCabalSpy: false,
    },
  ];

  const positionsData: PositionData[] = [
    {
      id: "P001",
      token: "TREE",
      bought: 1500.0,
      sold: 300.0,
      remaining: 1200.0,
      pnl: 245.67,
      pnlPercent: 18.5,
      suggestedAction: "sell",
    },
    {
      id: "P002",
      token: "FOREST",
      bought: 800.0,
      sold: 200.0,
      remaining: 600.0,
      pnl: -89.23,
      pnlPercent: -12.3,
      suggestedAction: "buy",
    },
    {
      id: "P003",
      token: "PLANT",
      bought: 2000.0,
      sold: 500.0,
      remaining: 1500.0,
      pnl: 567.89,
      pnlPercent: 34.2,
      suggestedAction: "sell",
    },
  ];

  const ordersData: OrderData[] = [
    {
      id: "O001",
      token: "TREE",
      type: "buy",
      amount: 1000.0,
      currentMC: "$235M",
      targetMC: "$250M",
    },
    {
      id: "O002",
      token: "FOREST",
      type: "sell",
      amount: 500.0,
      currentMC: "$180M",
      targetMC: "$150M",
    },
    {
      id: "O003",
      token: "PLANT",
      type: "buy",
      amount: 750.0,
      currentMC: "$92M",
      targetMC: "$120M",
    },
  ];

  const getTradeTypeColor = (type: "buy" | "sell") => {
    return type === "buy" ? "text-green-400" : "text-red-400";
  };

  const formatAmount = (amount: number) => {
    if (amount >= 1000) {
      return `${(amount / 1000).toFixed(2)}K`;
    }
    return amount.toLocaleString(undefined, { maximumFractionDigits: 1 });
  };

  const formatTotalUSD = (total: number) => {
    if (total >= 1000) {
      return `$${(total / 1000).toFixed(2)}K`;
    }
    return `$${total.toFixed(2)}`;
  };

  const formatPnL = (pnl: number) => {
    const formatted = Math.abs(pnl) >= 1000 ? `$${(Math.abs(pnl) / 1000).toFixed(2)}K` : `$${Math.abs(pnl).toFixed(2)}`;
    return pnl >= 0 ? `+${formatted}` : `-${formatted}`;
  };

  const getPnLColor = (pnl: number) => {
    return pnl >= 0 ? "text-green-400" : "text-red-400";
  };

  const formatSolBalance = (balance: number) => {
    return balance.toLocaleString(undefined, { maximumFractionDigits: 3 });
  };

  const formatBoughtSold = (amount: number, avgPrice: number) => {
    if (amount === 0) {
      return {
        amount: "$0",
        avg: "0 / 0",
      };
    }

    const formattedAmount = amount >= 1000 ? `$${(amount / 1000).toFixed(0)}K` : `$${amount}`;
    const formattedAvg = `${(amount / 1000).toFixed(1)}M / ${avgPrice}`;

    return {
      amount: formattedAmount,
      avg: formattedAvg,
    };
  };

  const formatTraderAmount = (amount: number, total: number, avg: number) => {
    const formattedAmount = amount >= 1000 ? `$${(amount / 1000).toFixed(0)}K` : `$${amount}`;
    const formattedTotal = `${total.toFixed(1)}M / ${avg.toFixed(0)}`;

    return {
      amount: formattedAmount,
      details: formattedTotal,
    };
  };

  const formatTraderPnL = (pnl: number) => {
    return pnl >= 1000 ? `+$${(pnl / 1000).toFixed(0)}M` : `+$${pnl}`;
  };

  const formatTraderRemaining = (remaining: number, percent: number) => {
    const formattedRemaining = remaining >= 1000 ? `$${(remaining / 1000).toFixed(0)}K` : `$${remaining.toFixed(0)}`;
    return {
      amount: formattedRemaining,
      percent: `${(percent * 100).toFixed(3)}%`,
    };
  };

  const formatDevTokenValue = (value: number) => {
    if (value === 0) return "$0";
    if (value >= 1000) {
      return `$${(value / 1000).toFixed(2)}K`;
    }
    return `$${value}`;
  };

  const getTokenStats = () => {
    const migrated = devTokensData.filter((token) => token.migrated).length;
    const nonMigrated = devTokensData.filter((token) => !token.migrated).length;
    const migratedPercentage = Math.round((migrated / (migrated + nonMigrated)) * 100);

    return {
      migrated,
      nonMigrated,
      migratedPercentage,
    };
  };

  const getHighlights = () => {
    const topMCapToken = devTokensData.reduce((prev, current) => (prev.marketCap > current.marketCap ? prev : current));

    const lastToken = devTokensData[0];

    return {
      topMCapToken,
      lastToken,
    };
  };

  return (
    <div className={`fixed bottom-0 left-[55px] right-[400px] z-[200] ${className}`}>
      <Card className="bg-black/90 backdrop-blur-xl border-gray-700 border-t border-x-0 border-b-0 rounded-t-lg rounded-b-none">
        <Tabs defaultValue="trades" className="w-full">
          <div className="flex items-center justify-between px-1 pt-1 border-b border-gray-700">
            <TabsList className="inline-flex h-7 w-fit bg-transparent justify-start gap-4">
              <TabsTrigger
                value="trades"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white data-[state=active]:font-bold bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Trades
              </TabsTrigger>
              <TabsTrigger
                value="positions"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Positions
              </TabsTrigger>
              <TabsTrigger
                value="orders"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Orders
              </TabsTrigger>
              <TabsTrigger
                value="holders"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Holders
              </TabsTrigger>
              <TabsTrigger
                value="traders"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Top Traders
              </TabsTrigger>
              <TabsTrigger
                value="devtokens"
                className="data-[state=active]:bg-transparent data-[state=active]:text-white data-[state=active]:border-b-2 data-[state=active]:border-white bg-transparent text-white/60 hover:text-white/80 py-1 px-0 text-xs border-b-2 border-transparent rounded-none"
              >
                Dev Tokens
              </TabsTrigger>
            </TabsList>

            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 mb-1 bg-gray-600 hover:bg-gray-500"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? <ChevronDown className="h-3 w-3" /> : <ChevronUp className="h-3 w-3" />}
            </Button>
          </div>

          {isExpanded && (
            <div className="px-2 pb-2 h-60">
              <TabsContent value="trades" className="mt-3">
                <div className="space-y-1 h-[230px] overflow-y-auto">
                  <div className="grid grid-cols-6 gap-4 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                    <span>Age</span>
                    <span>Type</span>
                    <span>MC</span>
                    <span>Amount</span>
                    <span>Total USD</span>
                    <span>Trader</span>
                  </div>

                  {tradesData.map((trade) => (
                    <div
                      key={trade.id}
                      className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                    >
                      <div className="grid grid-cols-6 gap-4 w-full text-[12px]">
                        <span className="text-gray-400">{trade.age}</span>
                        <div className="flex items-center gap-1">
                          {trade.type === "buy" ? (
                            <TrendingUp className="h-3 w-3 text-green-400" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-400" />
                          )}
                          <span className={getTradeTypeColor(trade.type)}>{trade.type}</span>
                        </div>
                        <span className="text-gray-400">{trade.mc}</span>
                        <span className="text-white">{formatAmount(trade.amount)}</span>
                        <span className={getTradeTypeColor(trade.type)}>{formatTotalUSD(trade.totalUSD)}</span>
                        <span className="text-gray-400 font-mono">{trade.trader}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="positions" className="mt-3">
                <div className="space-y-1 h-[230px] overflow-y-auto">
                  <div className="grid grid-cols-6 gap-4 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                    <span>Token</span>
                    <span>Bought</span>
                    <span>Sold</span>
                    <span>Remaining</span>
                    <span>PnL</span>
                    <span>Actions</span>
                  </div>

                  {positionsData.map((position) => (
                    <div
                      key={position.id}
                      className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                    >
                      <div className="grid grid-cols-6 gap-4 w-full text-[12px]">
                        <span className="text-white font-medium">{position.token}</span>
                        <span className="text-white">{formatAmount(position.bought)}</span>
                        <span className="text-white">{formatAmount(position.sold)}</span>
                        <span className="text-white">{formatAmount(position.remaining)}</span>
                        <div className="flex flex-col">
                          <span className={getPnLColor(position.pnl)}>{formatPnL(position.pnl)}</span>
                          <span className={`${getPnLColor(position.pnl)} text-xs`}>
                            {position.pnl >= 0 ? "+" : ""}
                            {position.pnlPercent.toFixed(1)}%
                          </span>
                        </div>
                        <button
                          className={`text-left font-medium ${
                            position.suggestedAction === "sell"
                              ? "text-red-400 hover:text-red-300"
                              : "text-green-400 hover:text-green-300"
                          }`}
                        >
                          {position.suggestedAction === "sell" ? "Sell" : "Buy"}
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="orders" className="mt-3">
                <div className="space-y-1 h-[230px] overflow-y-auto">
                  <div className="grid grid-cols-6 gap-4 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                    <span>Token</span>
                    <span>Type</span>
                    <span>Amount</span>
                    <span>Current MC</span>
                    <span>Target MC</span>
                    <span>Settings</span>
                  </div>

                  {ordersData.map((order) => (
                    <div
                      key={order.id}
                      className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                    >
                      <div className="grid grid-cols-6 gap-4 w-full text-[12px]">
                        <span className="text-white font-medium">{order.token}</span>
                        <div className="flex items-center gap-1">
                          {order.type === "buy" ? (
                            <TrendingUp className="h-3 w-3 text-green-400" />
                          ) : (
                            <TrendingDown className="h-3 w-3 text-red-400" />
                          )}
                          <span className={getTradeTypeColor(order.type)}>{order.type}</span>
                        </div>
                        <span className="text-white">{formatAmount(order.amount)}</span>
                        <span className="text-gray-400">{order.currentMC}</span>
                        <span className="text-gray-400">{order.targetMC}</span>
                        <span className="text-gray-400">Settings</span>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="holders" className="mt-3">
                <div className="flex gap-4 h-[230px]">
                  <div className="w-1/2 space-y-1 overflow-y-auto">
                    <div className="grid grid-cols-7 gap-2 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                      <span>Wallet</span>
                      <span>SOL Balance</span>
                      <span>Bought (Avg Buy)</span>
                      <span>Sold (Avg Sell)</span>
                      <span>InsightX</span>
                      <span>Bubblemaps.io</span>
                      <span>CabalSpy</span>
                    </div>

                    {holdersData.map((holder) => {
                      const boughtData = formatBoughtSold(holder.bought, holder.avgBuy);
                      const soldData = formatBoughtSold(holder.sold, holder.avgSell);

                      return (
                        <div
                          key={holder.id}
                          className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                        >
                          <div className="grid grid-cols-7 gap-2 w-full text-[12px]">
                            <span className="text-gray-400 font-mono">{holder.wallet}</span>
                            <span className="text-white">≈ {formatSolBalance(holder.solBalance)}</span>
                            <div className="flex flex-col">
                              <span className="text-green-400">{boughtData.amount}</span>
                              <span className="text-gray-400">{boughtData.avg}</span>
                            </div>
                            <div className="flex flex-col">
                              <span className="text-red-400">{soldData.amount}</span>
                              <span className="text-gray-400">{soldData.avg}</span>
                            </div>
                            <span className="text-center">{holder.hasInsightX ? "🔍" : ""}</span>
                            <span className="text-center">{holder.hasBubblemaps ? "🫧" : ""}</span>
                            <span className="text-center">{holder.hasCabalSpy ? "🕵️" : ""}</span>
                          </div>
                        </div>
                      );
                    })}
                  </div>

                  <div className="w-1/2 flex items-center justify-center border-l border-gray-700 pl-4">
                    <span className="text-gray-400 text-sm">Bubblemaps goes here</span>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="traders" className="mt-3">
                <div className="space-y-1 h-[230px] overflow-y-auto">
                  <div className="grid grid-cols-8 gap-2 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                    <span></span>
                    <span>Wallet</span>
                    <span>SOL Balance</span>
                    <span>Bought (Avg Buy)</span>
                    <span>Sold (Avg Sell)</span>
                    <span>PnL</span>
                    <span>Remaining</span>
                    <span>Edit</span>
                  </div>

                  {tradersData.map((trader, index) => {
                    const boughtData = formatTraderAmount(trader.boughtAmount, trader.boughtTotal, trader.boughtAvg);
                    const soldData = formatTraderAmount(trader.soldAmount, trader.soldTotal, trader.soldAvg);
                    const remainingData = formatTraderRemaining(trader.remaining, trader.remainingPercent);

                    return (
                      <div
                        key={trader.id}
                        className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                      >
                        <div className="grid grid-cols-8 gap-2 w-full text-[12px]">
                          <span className="text-gray-400">{index + 1}</span>
                          <div className="flex items-center gap-1">
                            <span className="text-gray-400 font-mono">{trader.wallet}</span>
                            {trader.hasSpecialIcons && (
                              <div className="flex gap-1">
                                {trader.hasSpecialIcons[0] && <span className="text-red-400">🔒</span>}
                                {trader.hasSpecialIcons[1] && <span className="text-purple-400">⚡</span>}
                                {trader.hasSpecialIcons[2] && <span className="text-yellow-400">🔥</span>}
                              </div>
                            )}
                          </div>
                          <span className="text-white">≈ {formatSolBalance(trader.solBalance)}</span>
                          <div className="flex flex-col">
                            <span className="text-green-400">{boughtData.amount}</span>
                            <span className="text-gray-400">{boughtData.details}</span>
                          </div>
                          <div className="flex flex-col">
                            <span className="text-red-400">{soldData.amount}</span>
                            <span className="text-gray-400">{soldData.details}</span>
                          </div>
                          <span className="text-green-400">{formatTraderPnL(trader.pnl)}</span>
                          <div className="flex flex-col">
                            <span className="text-white">{remainingData.amount}</span>
                            <span className="text-gray-400">{remainingData.percent}</span>
                          </div>
                          <button className="text-gray-400 hover:text-white text-left">⚙️</button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </TabsContent>

              <TabsContent value="devtokens" className="mt-3">
                <div className="flex gap-4 h-[230px]">
                  <div className="w-1/2 space-y-1 overflow-y-auto">
                    <div className="grid grid-cols-5 gap-3 text-xs text-gray-400 font-medium border-b border-gray-700 pb-2">
                      <span>Token ↓</span>
                      <span>Migrated</span>
                      <span>Market Cap</span>
                      <span>Liquidity</span>
                      <span>1h Volume</span>
                    </div>

                    {devTokensData.map((token) => (
                      <div
                        key={token.id}
                        className="flex flex-row flex-1 px-[16px] justify-start items-center max-h-[48px] min-h-[48px] bg-transparent hover:bg-gray-800/30 rounded"
                      >
                        <div className="grid grid-cols-5 gap-3 w-full text-[12px]">
                          <div className="flex items-center gap-2">
                            <span className="text-lg">{token.icon}</span>
                            <div className="flex flex-col">
                              <span className="text-white font-medium">{token.token}</span>
                              <span className="text-gray-400">{token.timeAgo}</span>
                            </div>
                          </div>
                          <div className="flex items-center">
                            {token.migrated ? (
                              <span className="text-green-400">✓</span>
                            ) : (
                              <span className="text-red-400">✗</span>
                            )}
                          </div>
                          <span className="text-white">{formatDevTokenValue(token.marketCap)}</span>
                          <span className="text-white">{formatDevTokenValue(token.liquidity)}</span>
                          <span className="text-white">{formatDevTokenValue(token.volume1h)}</span>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="w-1/2 border-l border-gray-700 pl-4 flex gap-3">
                    <div className="w-1/2 space-y-4">
                      <div>
                        <h4 className="text-gray-400 text-xs mb-2">Token Stats</h4>
                        <div className="space-y-1 text-xs">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-gray-400">
                              Migrated: <span className="text-white">{getTokenStats().migrated}</span>
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                            <span className="text-gray-400">
                              Non-migrated: <span className="text-white">{getTokenStats().nonMigrated}</span>
                            </span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-gray-400 text-xs mb-2">Highlights</h4>
                        <div className="space-y-2 text-xs">
                          <div>
                            <span className="text-gray-400">Top MCAP: </span>
                            <span className="text-white">
                              {getHighlights().topMCapToken.token.toUpperCase()} COIN (
                              {formatDevTokenValue(getHighlights().topMCapToken.marketCap)})
                            </span>
                          </div>
                          <div>
                            <span className="text-gray-400">Last Token Launched: </span>
                            <span className="text-white">{getHighlights().lastToken.timeAgo}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="w-1/2 flex items-center justify-center">
                      <div className="relative w-48 h-48">
                        <div className="w-48 h-48 rounded-full border-8 border-red-400"></div>
                        <div
                          className="absolute top-0 left-0 w-48 h-48 rounded-full border-4 border-green-400"
                          style={{
                            clipPath: `polygon(50% 50%, 50% 0%, ${50 + 50 * Math.cos((2 * Math.PI * getTokenStats().migratedPercentage) / 100 - Math.PI / 2)}% ${50 + 50 * Math.sin((2 * Math.PI * getTokenStats().migratedPercentage) / 100 - Math.PI / 2)}%, 50% 50%)`,
                          }}
                        ></div>
                        <div className="absolute inset-0 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-white text-base font-bold">{getTokenStats().migratedPercentage}%</div>
                            <div className="text-gray-400 text-xs">Migrated</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </TabsContent>
            </div>
          )}
        </Tabs>
      </Card>
    </div>
  );
};

export default TradingPanel;
