"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { usePredictionContracts } from "@/hooks/usePredictionContracts";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { CreatePredictionInput, NewCreatePredictionParams, WeatherCondition } from "@/types";
import { SMART_CONTRACT_CONFIG } from "@/utils/contracts";
import axios from "axios";
import { Calendar, DollarSign, Droplets, MapPin, Thermometer, Wind } from "lucide-react";
import React, { useCallback, useEffect, useState } from "react";
import { parseUnits } from "viem";

import { PolygonFeature } from "../hooks/usePolygonDraw";

interface CreatePredictionFormProps {
  polygonFeature: PolygonFeature;
  onSuccess?: (txHash: string) => void;
  onError?: (error: string) => void;
  title: string;
  description: string;
  shouldClearForm?: boolean;
  onFormCleared?: () => void;
}

const WEATHER_TYPES = [
  { value: "rainfall", label: "Rainfall", icon: Droplets },
  { value: "temperature", label: "Temperature", icon: Thermometer },
  { value: "wind", label: "Wind", icon: Wind },
] as const;

export default function CreatePredictionForm({
  polygonFeature,
  onSuccess,
  onError,
  title,
  description,
  shouldClearForm,
  onFormCleared,
}: CreatePredictionFormProps) {
  const { getJWT } = usePrivyAuth();
  const { createPrediction, isLoading, error, etherToWei, weatherConditionToString } = usePredictionContracts();

  const [formData, setFormData] = useState({
    weatherType: "",
    startDate: "",
    endDate: "",
    initialLiquidity: "0",
    // Rainfall fields
    rainfallThreshold: 10,
    rainfallOperator: "gte" as "gte" | "lte",
    // Temperature fields
    minTemp: 20,
    maxTemp: 30,
    tempUnit: "celsius" as "celsius" | "fahrenheit",
    // Wind fields
    windSpeed: 50,
    windOperator: "gte" as "gte" | "lte",
    windUnit: "km/h" as "km/h" | "mph",
  });

  const resetFormData = useCallback(() => {
    setFormData({
      weatherType: "",
      startDate: "",
      endDate: "",
      initialLiquidity: "0",
      rainfallThreshold: 10,
      rainfallOperator: "gte",
      minTemp: 20,
      maxTemp: 30,
      tempUnit: "celsius",
      windSpeed: 50,
      windOperator: "gte",
      windUnit: "km/h",
    });
    onFormCleared?.();
  }, [onFormCleared]);

  useEffect(() => {
    if (shouldClearForm) {
      resetFormData();
    }
  }, [shouldClearForm, resetFormData]);

  const handleInputChange = useCallback((field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  }, []);

  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!polygonFeature) {
        onError?.("No polygon selected");
        resetFormData();
        return;
      }

      if (!title.trim()) {
        onError?.("Title is required");
        resetFormData();
        return;
      }

      try {
        const startTimestamp = Math.floor(new Date(formData.startDate).getTime() / 1000);
        const endTimestamp = Math.floor(new Date(formData.endDate).getTime() / 1000);

        if (endTimestamp <= startTimestamp) {
          throw new Error("End date must be after start date");
        }

        if (endTimestamp <= Math.floor(Date.now() / 1000)) {
          throw new Error("End date must be in the future");
        }

        let contractWeatherCondition: WeatherCondition;
        if (formData.weatherType === "rainfall") {
          contractWeatherCondition = {
            type: "rainfall",
            data: {
              threshold: Number(formData.rainfallThreshold),
              unit: "mm",
              operator: formData.rainfallOperator,
            },
          };
        } else if (formData.weatherType === "temperature") {
          contractWeatherCondition = {
            type: "temperature",
            data: {
              minTemp: Number(formData.minTemp),
              maxTemp: Number(formData.maxTemp),
              unit: formData.tempUnit,
            },
          };
        } else if (formData.weatherType === "wind") {
          contractWeatherCondition = {
            type: "wind",
            data: {
              maxSpeed: Number(formData.windSpeed),
              unit: formData.windUnit,
              operator: formData.windOperator,
            },
          };
        } else {
          throw new Error("Please select a weather condition type");
        }

        const conditionString = weatherConditionToString(contractWeatherCondition);

        const predictionPayload = {
          title: title.trim(),
          description: description.trim(),
          polygonId: polygonFeature.properties.polygon_id,
          polygon: polygonFeature.geometry,
          eventType: formData.weatherType,
          eventConfig: contractWeatherCondition.data,
          startTime: startTimestamp,
          endTime: endTimestamp,
        };

        const signatureResponse = await axios.post("https://api.yby.energy/api/predictions", predictionPayload, {
          headers: {
            Authorization: `Bearer ${await getJWT()}`,
          },
        });

        const signatureData = signatureResponse.data;

        if (!signatureData.signature) {
          throw new Error("No signature received from backend");
        }

        const signature = signatureData.signature;

        console.log({ conditionString });

        const predictionInput: CreatePredictionInput = {
          polygonId: polygonFeature.properties.polygon_id,
          polygonCid: "", // TODO: Get from backend
          conditions: ["Yes", "No"], // TODO: This will be change to dynamic conditions
          // question: conditionString, //TODO: This field will be added later
          outcomeSlot: BigInt(2),
          startDate: BigInt(startTimestamp),
          endDate: BigInt(endTimestamp),
          resolvers: [SMART_CONTRACT_CONFIG.resolverAddress],
          resolved: false,
        };

        const params: NewCreatePredictionParams = {
          input: predictionInput,
          signature,
          initialLiquidity:
            parseFloat(formData.initialLiquidity) > 0 ? parseUnits(formData.initialLiquidity, 6).toString() : "0",
        };

        const txHash = await createPrediction(params);

        onSuccess?.(txHash);

        resetFormData();
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Failed to create prediction";
        onError?.(errorMessage);

        resetFormData();
      }
    },
    [
      formData,
      polygonFeature,
      createPrediction,
      etherToWei,
      onSuccess,
      onError,
      title,
      description,
      resetFormData,
      weatherConditionToString,
    ],
  );

  const isFormValid =
    formData.weatherType &&
    formData.startDate &&
    formData.endDate &&
    polygonFeature &&
    parseFloat(formData.initialLiquidity) > 0 &&
    title.trim();

  return (
    <Card className="w-full bg-black/85 border-gray-700">
      <CardContent className="space-y-4 pt-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="p-3 bg-gray-900/50 rounded-lg border border-gray-700">
            <div className="flex items-center gap-2 mb-2">
              <MapPin className="h-4 w-4 text-green-400" />
              <span className="text-sm font-semibold text-green-400">Selected Area</span>
            </div>
            <div className="text-xs text-gray-300">
              <div>Area: {polygonFeature?.properties.area_ha.toFixed(2)} ha</div>
              <div className="truncate">ID: {polygonFeature?.properties.polygon_id}</div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="weatherType" className="text-sm text-gray-300">
              Weather Type
            </Label>
            <Select value={formData.weatherType} onValueChange={(value) => handleInputChange("weatherType", value)}>
              <SelectTrigger className="bg-gray-900 border-gray-600 text-white">
                <SelectValue placeholder="Select weather type..." />
              </SelectTrigger>
              <SelectContent className="bg-gray-900 border-gray-600">
                {WEATHER_TYPES.map((type) => {
                  const IconComponent = type.icon;
                  return (
                    <SelectItem key={type.value} value={type.value} className="text-white hover:bg-gray-700">
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" />
                        {type.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {formData.weatherType === "rainfall" && (
            <div className="space-y-3 p-3 bg-blue-900/20 rounded-lg border border-blue-700/30">
              <div className="flex items-center gap-2">
                <Droplets className="h-4 w-4 text-blue-400" />
                <span className="text-sm font-semibold text-blue-400">Rainfall Configuration</span>
              </div>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs text-gray-300">Threshold (mm)</Label>
                  <Input
                    type="number"
                    value={formData.rainfallThreshold}
                    onChange={(e) => handleInputChange("rainfallThreshold", e.target.value)}
                    className="bg-gray-900 border-gray-600 text-white text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-300">Operator</Label>
                  <Select
                    value={formData.rainfallOperator}
                    onValueChange={(value) => handleInputChange("rainfallOperator", value)}
                  >
                    <SelectTrigger className="bg-gray-900 border-gray-600 text-white text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-600">
                      <SelectItem value="gte" className="text-white">
                        ≥ (Greater or equal)
                      </SelectItem>
                      <SelectItem value="lte" className="text-white">
                        ≤ (Less or equal)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {formData.weatherType === "temperature" && (
            <div className="space-y-3 p-3 bg-orange-900/20 rounded-lg border border-orange-700/30">
              <div className="flex items-center gap-2">
                <Thermometer className="h-4 w-4 text-orange-400" />
                <span className="text-sm font-semibold text-orange-400">Temperature Configuration</span>
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <Label className="text-xs text-gray-300">Min Temp</Label>
                  <Input
                    type="number"
                    value={formData.minTemp}
                    onChange={(e) => handleInputChange("minTemp", e.target.value)}
                    className="bg-gray-900 border-gray-600 text-white text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-300">Max Temp</Label>
                  <Input
                    type="number"
                    value={formData.maxTemp}
                    onChange={(e) => handleInputChange("maxTemp", e.target.value)}
                    className="bg-gray-900 border-gray-600 text-white text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-300">Unit</Label>
                  <Select value={formData.tempUnit} onValueChange={(value) => handleInputChange("tempUnit", value)}>
                    <SelectTrigger className="bg-gray-900 border-gray-600 text-white text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-600">
                      <SelectItem value="celsius" className="text-white">
                        Celsius (°C)
                      </SelectItem>
                      <SelectItem value="fahrenheit" className="text-white">
                        Fahrenheit (°F)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          {formData.weatherType === "wind" && (
            <div className="space-y-3 p-3 bg-green-900/20 rounded-lg border border-green-700/30">
              <div className="flex items-center gap-2">
                <Wind className="h-4 w-4 text-green-400" />
                <span className="text-sm font-semibold text-green-400">Wind Configuration</span>
              </div>
              <div className="grid grid-cols-3 gap-3">
                <div>
                  <Label className="text-xs text-gray-300">Max Speed</Label>
                  <Input
                    type="number"
                    value={formData.windSpeed}
                    onChange={(e) => handleInputChange("windSpeed", e.target.value)}
                    className="bg-gray-900 border-gray-600 text-white text-sm"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-300">Unit</Label>
                  <Select value={formData.windUnit} onValueChange={(value) => handleInputChange("windUnit", value)}>
                    <SelectTrigger className="bg-gray-900 border-gray-600 text-white text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-600">
                      <SelectItem value="km/h" className="text-white">
                        km/h
                      </SelectItem>
                      <SelectItem value="mph" className="text-white">
                        mph
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label className="text-xs text-gray-300">Operator</Label>
                  <Select
                    value={formData.windOperator}
                    onValueChange={(value) => handleInputChange("windOperator", value)}
                  >
                    <SelectTrigger className="bg-gray-900 border-gray-600 text-white text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="bg-gray-900 border-gray-600">
                      <SelectItem value="gte" className="text-white">
                        ≥ (Greater or equal)
                      </SelectItem>
                      <SelectItem value="lte" className="text-white">
                        ≤ (Less or equal)
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="startDate" className="text-sm text-gray-300">
              Start Date
            </Label>
            <Input
              id="startDate"
              type="datetime-local"
              value={formData.startDate}
              onChange={(e) => handleInputChange("startDate", e.target.value)}
              className="bg-gray-900 border-gray-600 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="endDate" className="text-sm text-gray-300">
              End Date
            </Label>
            <Input
              id="endDate"
              type="datetime-local"
              value={formData.endDate}
              onChange={(e) => handleInputChange("endDate", e.target.value)}
              className="bg-gray-900 border-gray-600 text-white"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="initialLiquidity" className="text-sm text-gray-300">
              Initial Liquidity (USDC)
            </Label>
            <div className="relative">
              <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                id="initialLiquidity"
                type="number"
                value={formData.initialLiquidity}
                onChange={(e) => handleInputChange("initialLiquidity", e.target.value)}
                min="0"
                step="0.01"
                placeholder="0.00"
                className="bg-gray-900 border-gray-600 text-white pl-10"
              />
            </div>
          </div>

          <Button
            type="submit"
            disabled={!isFormValid || isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating Prediction...
              </>
            ) : (
              <>
                <Calendar className="h-4 w-4 mr-2" />
                Create Prediction
              </>
            )}
          </Button>

          {error && (
            <div className="text-red-400 text-sm p-2 bg-red-900/20 border border-red-700/30 rounded">{error}</div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
