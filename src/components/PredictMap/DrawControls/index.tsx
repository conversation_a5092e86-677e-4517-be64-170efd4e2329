"use client";

import { But<PERSON> } from "@/components/ui/button";
import MapboxDraw from "@mapbox/mapbox-gl-draw";
import { Plus, Trash } from "lucide-react";
import React from "react";

interface DrawControlsProps {
  draw: MapboxDraw | null;
  hasPolygon: boolean;
  onDeletePolygon: () => void;
}

const DrawControls: React.FC<DrawControlsProps> = ({ draw, hasPolygon, onDeletePolygon }) => {
  const handleStartDrawing = () => {
    if (draw) {
      draw.changeMode("draw_polygon");
    }
  };

  const handleDeletePolygon = () => {
    onDeletePolygon();
  };

  return (
    <div className="absolute top-[30px] sm:top-[10px] left-0 mt-[60px] ml-[75px] flex flex-col gap-2">
      <Button
        onClick={handleStartDrawing}
        className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-black/80 backdrop-blur-md flex items-center justify-center px-2 gap-2"
        size="sm"
        title="Draw Polygon"
      >
        <Plus className="h-4 w-4" />
        <span>Polygon</span>
      </Button>

      {hasPolygon && (
        <Button
          onClick={handleDeletePolygon}
          className="border border-[#ffffff56] hover:border-[#ffffff] hover:bg-[#ffffff56] overflow-hidden rounded-md h-[40px] w-fit bg-red-600/80 backdrop-blur-md flex items-center justify-center px-2 gap-2"
          size="sm"
          title="Delete Polygon"
        >
          <Trash className="h-4 w-4" />
          <span>Remove</span>
        </Button>
      )}
    </div>
  );
};

export default DrawControls;
