import * as turf from "@turf/turf";
import { useCallback, useState } from "react";

export interface PolygonFeature {
  id: string;
  geometry: {
    type: "Polygon";
    coordinates: number[][][];
  };
  properties: {
    polygon_id: string;
    area_ha: number;
    datetime: string;
  };
}

interface UsePolygonDrawReturn {
  polygonFeature: PolygonFeature | null;
  setPolygonFeature: (feature: PolygonFeature | null) => void;
  calculateArea: (geometry: PolygonFeature["geometry"]) => number;
  generateGUID: () => string;
  formatTimestamp: () => string;
  clearPolygon: () => void;
  regeneratePolygonId: () => void;
  generateEeFeatureCode: (feature: PolygonFeature) => string;
}

export const usePolygonDraw = (): UsePolygonDrawReturn => {
  const [polygonFeature, setPolygonFeature] = useState<PolygonFeature | null>(null);

  const calculateArea = useCallback((geometry: PolygonFeature["geometry"]): number => {
    if (!geometry || !geometry.coordinates) return 0;
    const areaM2 = turf.area(geometry);
    return areaM2 / 10000;
  }, []);

  const generateGUID = useCallback((): string => {
    if (crypto && crypto.randomUUID) {
      return crypto.randomUUID();
    }
    return "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
      const r = (Math.random() * 16) | 0;
      const v = c === "x" ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });
  }, []);

  const formatTimestamp = useCallback((): string => {
    const d = new Date();
    const pad = (n: number) => n.toString().padStart(2, "0");
    const yyyy = d.getFullYear();
    const mm = pad(d.getMonth() + 1);
    const dd = pad(d.getDate());
    const hh = pad(d.getHours());
    const min = pad(d.getMinutes());
    const ss = pad(d.getSeconds());
    return `${yyyy}${mm}${dd}_${hh}:${min}:${ss}`;
  }, []);

  const clearPolygon = useCallback(() => {
    setPolygonFeature(null);
  }, []);

  const regeneratePolygonId = useCallback(() => {
    if (polygonFeature) {
      const newPolygonId =
        crypto?.randomUUID?.() ||
        "xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g, function (c) {
          const r = (Math.random() * 16) | 0;
          const v = c === "x" ? r : (r & 0x3) | 0x8;
          return v.toString(16);
        });

      const updatedFeature: PolygonFeature = {
        ...polygonFeature,
        properties: {
          ...polygonFeature.properties,
          polygon_id: newPolygonId,
          datetime: formatTimestamp(),
        },
      };

      setPolygonFeature(updatedFeature);
    }
  }, [polygonFeature, formatTimestamp]);

  const generateEeFeatureCode = useCallback((feature: PolygonFeature): string => {
    const coordsString = JSON.stringify(feature.geometry.coordinates, null, 2);
    const propsString = JSON.stringify(feature.properties, null, 2);

    return `ee.Feature(
  ee.Geometry.Polygon(${coordsString}),
  ${propsString}
);`;
  }, []);

  return {
    polygonFeature,
    setPolygonFeature,
    calculateArea,
    generateGUID,
    formatTimestamp,
    clearPolygon,
    regeneratePolygonId,
    generateEeFeatureCode,
  };
};
