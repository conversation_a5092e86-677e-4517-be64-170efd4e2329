import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import usePrivyAuth from "@/hooks/usePrivyAuth";
import { cn } from "@/lib/utils";

import StepContent from "./StepContent";
import { ClaimFormProps } from "./types";
import { useClaimForm } from "./useClaimForm";

const getUserEmail = (user: any): string | undefined => {
  if (!user) return undefined;

  if (user.email?.address) return user.email.address;

  if (user.linkedAccounts?.length) {
    const emailAccount = user.linkedAccounts.find((account: any) => account.email);
    if (emailAccount?.email) return emailAccount.email;
  }

  if (user.google?.email) return user.google.email;

  return undefined;
};

const ClaimForm = ({
  children,
  propertyId,
  isClaimed,
  claimId,
}: ClaimFormProps & { isClaimed: boolean; claimId: string | null }) => {
  const { user } = usePrivyAuth();
  const userEmail = getUserEmail(user);

  const { open, formData, setFormData, handleModalChange, handleSubmit, isSubmitButtonDisabled, requestOwnership } =
    useClaimForm(propertyId, userEmail, isClaimed, claimId);

  return (
    <Dialog open={open} onOpenChange={handleModalChange}>
      <DialogTrigger asChild>{children || <Button className="w-full">Claim Property</Button>}</DialogTrigger>
      <DialogContent className="sm:max-w-[700px] border-neutral-800 bg-neutral-950">
        <DialogHeader>
          <DialogTitle className="text-neutral-100">Claim Property</DialogTitle>
        </DialogHeader>

        <div className="py-4">
          <div className="flex justify-center mb-8">
            <div className="flex items-center">
              <div className="text-sm text-neutral-400">Insert an email to claim the property</div>
            </div>
          </div>

          <StepContent
            formData={formData}
            setFormData={setFormData}
            onCaptchaChange={(token) => setFormData((prev) => ({ ...prev, captchaToken: token || "" }))}
          />

          <div className="flex justify-end mt-8">
            <Button
              onClick={handleSubmit}
              disabled={isSubmitButtonDisabled()}
              className={cn(
                "w-24 text-white",
                requestOwnership.isPending ? "bg-blue-600/50 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700",
              )}
            >
              {requestOwnership.isPending ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  <span>Sending</span>
                </div>
              ) : (
                "Submit"
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ClaimForm;
