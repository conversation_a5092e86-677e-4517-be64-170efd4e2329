/* @import url("https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300..700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Ubuntu+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=IBM+Plex+Mono:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;1,100;1,200;1,300;1,400;1,500;1,600;1,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Ubuntu+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap");
@import url("https://fonts.googleapis.com/css2?family=Quicksand:wght@300..700&display=swap"); */

/* Denim Font */
@font-face {
  font-family: "Denim";
  src:
    url("/fonts/denim/DenimINK-Regular.woff2") format("woff2"),
    url("/fonts/denim/DenimINK-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* System Mono Font */
@font-face {
  font-family: "SystemMono";
  src:
    url("/fonts/system-mono/FTSystemMono-Regular.woff2") format("woff2"),
    url("/fonts/system-mono/FTSystemMono-Regular.woff") format("woff"),
    url("/fonts/system-mono/FTSystemMono-Regular.ttf") format("truetype"),
    url("/fonts/system-mono/FTSystemMono-Regular.otf") format("opentype");
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  font-family: "SystemMono", sans-serif;
}

::-webkit-scrollbar {
  width: 2px;
  height: 2px;
}

::-webkit-scrollbar-thumb {
  background-color: yellow;
  border-radius: 10px;
  border: 3px solid #fff;
}

::-webkit-scrollbar-track {
  background-color: #333333;
}

html {
  scroll-behavior: smooth !important;
}

.first-card-path {
  clip-path: polygon(90% 0, 100% 10%, 100% 100%, 0 100%, 0 0);
}

.second-card-path {
  clip-path: polygon(90% 0, 100% 6%, 100% 100%, 0 100%, 0 0);
}

.third-card-path {
  clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 6.5%);
}

.fourth-card-path {
  clip-path: polygon(10% 0, 100% 0, 100% 100%, 0 100%, 0 10%);
}

.btn-clip-path {
  clip-path: polygon(19% 0, 83% 0%, 100% 0, 100% 77%, 83% 100%, 20% 100%, 0 100%, 0 24%);
}

.btn-custom-clip-path {
  clip-path: polygon(50% 0%, 100% 0, 100% 35%, 100% 70%, 100% 100%, 51% 100%, 15% 100%, 0 66%, 0% 35%, 0 0);
}

.header-card-clip-path {
  clip-path: polygon(36.5% 100%, 39% 93%, 64% 94%, 66% 100%, 97% 100%, 100% 77%, 100% 0%, 0% 0%, 0% 100%);
}

.search-box-clip-path {
  clip-path: polygon(0 0, 95% 0, 100% 5%, 100% 95%, 100% 100%, 5% 100%, 0 95%);
}

.layers-box-clip-path {
  clip-path: polygon(0 0, 95% 0, 100% 5%, 100% 95%, 100% 100%, 5% 100%, 0 95%);
}

.mapboxgl-popup-content {
  background: none !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.mapboxgl-popup-tip {
  display: none !important;
}

.delay-0-25s {
  animation-delay: 0.25s;
}

.delay-0-5s {
  animation-delay: 0.5s;
}

.delay-1s {
  animation-delay: 1s;
}

.mapboxgl-ctrl-logo {
  display: none !important;
}

.mapboxgl-ctrl-attrib {
  display: none !important;
}

.homescreen-gradient-bg {
  background: rgb(25, 25, 25);
  background: linear-gradient(90deg, rgba(25, 25, 25, 1) 0%, rgba(57, 57, 57, 1) 49%, rgba(25, 25, 25, 1) 100%);
}

.width-n-height-transition {
  transition:
    width 0.3s ease-in-out,
    height 0.3s ease-in-out;
}

.minimizer-btn {
  clip-path: polygon(0px 15%, 100% 0px, 100% 100%, 0px 85%);
}

#privy-modal-content {
  border-radius: 0 !important;
}

#privy-modal-content img {
  margin-bottom: 2em;
}

#privy-modal-content > div > div > div:nth-child(3) > div > button {
  border-radius: 0 !important;
  border: 0.5px solid rgba(172, 255, 70, 0.1) !important;
  font-weight: 700 !important;
}

#privy-modal-content > div > div > div:nth-child(2) > div > button {
  border-radius: 0 !important;
  border: 0.5px solid rgba(172, 255, 70, 0.1) !important;
  font-weight: 700 !important;
}

#privy-modal-content > div > div > div:nth-child(2) > button {
  border-radius: 0 !important;
  border: 0.5px solid rgba(172, 255, 70, 0.1) !important;
  font-weight: 700 !important;
}

/* #privy-modal-content > div > div > div:nth-child(2) > div > div > div {
  border-radius: 0 !important;
  border: 0.5px solid rgba(172, 255, 70, 0.1) !important;
} */

#privy-modal-content > div > div > div:nth-child(3) > div > button > span {
  font-family: "SystemMono", sans-serif !important;
  font-weight: 700 !important;
}

#privy-modal-content a {
  display: none;
}

#privy-dialog-title {
  font-size: 1.3rem;
  font-weight: 700;
  color: #acff46;
  font-family: "SystemMono", sans-serif;
}

/* Exemplos de classes para usar as fontes locais */
.denim-font {
  font-family: "Denim", sans-serif;
}

.system-mono-font {
  font-family: "SystemMono", monospace;
}

/* Mapbox GL Draw Custom Styles */
.mapboxgl-ctrl-group {
  border-radius: 8px !important;
  box-shadow:
    0 4px 6px -1px rgb(0 0 0 / 0.1),
    0 2px 4px -2px rgb(0 0 0 / 0.1) !important;
}

.mapboxgl-ctrl-group button {
  background-color: white !important;
  border: none !important;
  border-radius: 8px !important;
  transition: background-color 0.2s ease-in-out !important;
}

.mapboxgl-ctrl-group button:hover {
  background-color: #f3f4f6 !important;
}

.mapboxgl-ctrl-group button.mapbox-gl-draw_ctrl-draw-btn {
  background-color: #3b82f6 !important;
  color: white !important;
}

.mapboxgl-ctrl-group button.mapbox-gl-draw_ctrl-draw-btn:hover {
  background-color: #2563eb !important;
}

/* Polygon drawing styles */
.mapbox-gl-draw_polygon-fill-inactive {
  fill: rgba(59, 130, 246, 0.1) !important;
  fill-opacity: 0.1 !important;
}

.mapbox-gl-draw_polygon-fill-active {
  fill: rgba(59, 130, 246, 0.2) !important;
  fill-opacity: 0.2 !important;
}

.mapbox-gl-draw_polygon-stroke-inactive {
  stroke: #3b82f6 !important;
  stroke-width: 2 !important;
  stroke-opacity: 0.8 !important;
}

.mapbox-gl-draw_polygon-stroke-active {
  stroke: #1d4ed8 !important;
  stroke-width: 3 !important;
  stroke-opacity: 1 !important;
}

.mapbox-gl-draw_vertex {
  fill: #ffffff !important;
  stroke: #3b82f6 !important;
  stroke-width: 2 !important;
  r: 4 !important;
}

.mapbox-gl-draw_vertex.mapbox-gl-draw_vertex-active {
  fill: #3b82f6 !important;
  stroke: #1d4ed8 !important;
  stroke-width: 3 !important;
  r: 5 !important;
}
