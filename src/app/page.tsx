"use client";

import usePrivyAuth from "@/hooks/usePrivyAuth";
import { useEffect } from "react";

export default function Home() {
  const { ready, authenticated } = usePrivyAuth();

  useEffect(() => {
    // The AppWrapper will handle auto-triggering login for unauthenticated users
    // This page just serves as the entry point
  }, [ready, authenticated]);

  // Return a minimal loading state while AppWrapper handles authentication
  return (
    <div className="min-h-screen flex items-center justify-center bg-primary-dark">
      <div className="text-white text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-4"></div>
        <p className="text-sm opacity-70">Initializing...</p>
      </div>
    </div>
  );
}
