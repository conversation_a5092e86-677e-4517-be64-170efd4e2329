"use client";

import IbiIcon from "@/components/IbiUi/IbiIcon";
import PumpMap from "@/components/PumpMap";
import React from "react";

const PumpPage: React.FC = () => {
  const pumpData = {
    metrics: [
      { label: "Price", value: "$12,458" },
      { label: "Total Volume", value: "$1,224.50" },
      { label: "Owners", value: "305,221" },
    ],
  };

  return (
    <div className="w-full h-screen relative">
      <div className="absolute inset-0">
        <PumpMap />
      </div>

      <div className="absolute bottom-16 left-4 z-20">
        <div className="bg-black/85 backdrop-blur-md border border-[#ffffff2a] rounded-lg p-4">
          <div className="flex items-center gap-8">
            <div className="p-2 rounded-lg border bg-purple-500/20 text-purple-400 border-purple-500/30 flex items-center justify-center">
              <IbiIcon icon="ic:baseline-rocket" className="w-5 h-5" />
            </div>

            {pumpData.metrics.map((metric, index) => (
              <div key={index} className="flex flex-col items-center">
                <span className="text-xs text-gray-400 mb-1">{metric.label}</span>
                <span className="text-lg font-semibold text-white">{metric.value}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PumpPage;
