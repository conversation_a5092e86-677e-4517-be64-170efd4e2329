import { useAppHeaderSelector } from "@/components/Headers/EntryHeader/header.store";
import { PrivyAuthContextProps } from "@/types";
import { useLogout, usePrivy, useWallets } from "@privy-io/react-auth";
import { useMemo } from "react";
import { createContext } from "use-context-selector";

const initialValue: PrivyAuthContextProps = {
  wallets: null,
  ready: false,
  authenticated: false,
  onLogin: () => { },
  onLogout: () => { },
  confirmLogout: () => { },
  onCreateWallet: async () => { },
  getJWT: async () => "",
};

const PrivyAuthContext = createContext<PrivyAuthContextProps>(initialValue);

export function PrivyAuthProvider({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const { ready, authenticated, user, createWallet, getAccessToken, login } = usePrivy();
  const { wallets } = useWallets();
  const toggleConfirmLogout = useAppHeaderSelector.use.toggleConfirmLogout();

  // Use the useLogout hook with external redirect on success
  const { logout } = useLogout({
    onSuccess: () => {
      // Redirect to external URL after successful logout
      window.location.href = "https://app.ibi.cash";
    },
    onError: (error) => {
      console.error("Logout failed", error);
      // Still redirect to external URL even if logout fails
      window.location.href = "https://app.ibi.cash";
    },
  });

  const onLogin = async () => {
    login();
  };

  const onLogout = () => {
    if (authenticated) {
      toggleConfirmLogout();
    }
  };

  const confirmLogout = (direct?: boolean) => {
    if (authenticated) {
      !direct && toggleConfirmLogout();
      logout();
    }
  };

  const onCreateWallet = async () => {
    await createWallet();
  };

  const getJWT = async () => {
    const jwt = await getAccessToken();
    return jwt;
  };

  const value = useMemo(
    () => ({
      user,
      wallets,
      ready,
      authenticated,
      onLogin,
      onLogout,
      confirmLogout,
      onCreateWallet,
      getJWT,
    }),
    [user, wallets, ready, onLogin, onLogout, confirmLogout, onCreateWallet, getJWT, authenticated],
  );

  return <PrivyAuthContext.Provider value={value}>{children}</PrivyAuthContext.Provider>;
}

export default PrivyAuthContext;
