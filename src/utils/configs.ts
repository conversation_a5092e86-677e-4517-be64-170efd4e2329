import localFont from "@next/font/local";
import { PrivyClientConfig } from "@privy-io/react-auth";
import { NextTopLoaderProps } from "nextjs-toploader";
import { cookieStorage, createConfig, createStorage, http } from "wagmi";
import { sepolia } from "wagmi/chains";
import { coinbaseWallet, injected } from "wagmi/connectors";

export const privyConfig: PrivyClientConfig = {
  appearance: {
    theme: "#000000",
    accentColor: "#acff46",
    logo: "https://ik.imagekit.io/97q72hphb/Union.png?updatedAt=1744141959581",
    landingHeader: "",
  },
  loginMethods: ["email", "wallet", "google", "sms", "github", "discord"],
  embeddedWallets: {
    createOnLogin: "users-without-wallets",
  },
  defaultChain: sepolia,
  supportedChains: [sepolia],
};

export const nextLoaderConfig: NextTopLoaderProps = {
  color: "#fff",
  initialPosition: 0.08,
  crawlSpeed: 200,
  height: 3,
  crawl: true,
  showSpinner: true,
  easing: "ease",
  speed: 200,
  shadow: "0 0 10px #acff46,0 0 5px #acff46",
  template:
    '<div class="bar" role="bar"><div class="peg"></div></div> <div class="spinner" role="spinner"><div class="spinner-icon"></div></div>',
  zIndex: 1600,
  showAtBottom: false,
};

export const ABC_WHITE_PLUS_BOLD = localFont({
  src: [
    {
      path: "../static/fonts/ABCWhyteInktrap-Bold-Trial.otf",
      weight: "800",
    },
  ],
  variable: "--font-abcwhiteplus",
});

export const ABC_WHITE_PLUS_REGULAR = localFont({
  src: [
    {
      path: "../static/fonts/ABCWhyteInktrap-Regular-Trial.otf",
      weight: "800",
    },
  ],
  variable: "--font-abcwhiteplus",
});

export const ABC_WHITE_PLUS_LIGHT = localFont({
  src: [
    {
      path: "../static/fonts/ABCWhyteInktrap-Light-Trial.otf",
      weight: "800",
    },
  ],
  variable: "--font-abcwhiteplus",
});

export const ABC_WHITE_PLUS_THIN = localFont({
  src: [
    {
      path: "../static/fonts/ABCWhyteInktrap-Thin-Trial.otf",
      weight: "800",
    },
  ],
  variable: "--font-abcwhiteplus",
});

export function getConfig() {
  return createConfig({
    chains: [sepolia],
    connectors: [injected(), coinbaseWallet()],
    storage: createStorage({
      storage: cookieStorage,
    }),
    ssr: true,
    transports: {
      [sepolia.id]: http(),
    },
  });
}

declare module "wagmi" {
  interface Register {
    config: ReturnType<typeof getConfig>;
  }
}
